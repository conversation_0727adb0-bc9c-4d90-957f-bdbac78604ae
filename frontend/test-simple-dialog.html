<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hộp <PERSON><PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root { --primary-color: #e53e3e; }
        .bg-primary { background-color: var(--primary-color); }
        .text-primary { color: var(--primary-color); }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">Test Hộp Thoại Session</h1>
        
        <!-- Login Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">Đăng Nhập Test</h2>
            <div class="flex space-x-4">
                <button id="loginBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg">
                    <i class="fas fa-sign-in-alt mr-2"></i>Đăng Nhập (Token 10 giây)
                </button>
                <button id="logoutBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg">
                    <i class="fas fa-sign-out-alt mr-2"></i>Đăng Xuất
                </button>
            </div>
            <div id="loginStatus" class="mt-4 p-3 bg-gray-50 rounded">
                <p class="text-gray-600">Chưa đăng nhập</p>
            </div>
        </div>

        <!-- Test Dialogs -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">Test Hộp Thoại</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button id="testWarning" class="bg-yellow-600 hover:bg-yellow-700 text-white py-3 rounded-lg">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Test Warning
                </button>
                <button id="testExpired" class="bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg">
                    <i class="fas fa-clock mr-2"></i>Test Expired
                </button>
            </div>
        </div>

        <!-- Timer Display -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">Timer</h2>
            <div class="text-center">
                <div id="timerDisplay" class="text-4xl font-mono text-blue-600">00:00</div>
                <p class="text-gray-600 mt-2">Thời gian đến khi auto logout</p>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-bold text-blue-800 mb-3">
                <i class="fas fa-info-circle mr-2"></i>Hướng Dẫn Test
            </h3>
            <ol class="list-decimal list-inside space-y-2 text-blue-700">
                <li>Click "Đăng Nhập" để tạo session 10 giây</li>
                <li>Sau 5 giây sẽ hiển thị Warning dialog</li>
                <li>Sau 10 giây sẽ hiển thị Expired dialog</li>
                <li>Hoặc click nút test để xem dialog ngay</li>
            </ol>
        </div>
    </div>

    <!-- Include auth.js -->
    <script>
        const API_BASE_URL = 'http://localhost:3000/api';
    </script>
    <script src="js/auth.js"></script>

    <script>
        let loginTime = null;
        let timerInterval = null;

        function updateLoginStatus() {
            const statusDiv = document.getElementById('loginStatus');
            const isLoggedIn = window.auth && window.auth.isLoggedIn();
            
            if (isLoggedIn) {
                const user = window.auth.getCurrentUser();
                statusDiv.innerHTML = `
                    <div class="flex items-center text-green-600">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span>Đã đăng nhập: ${user.full_name || user.email}</span>
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="flex items-center text-red-600">
                        <i class="fas fa-times-circle mr-2"></i>
                        <span>Chưa đăng nhập</span>
                    </div>
                `;
            }
        }

        function startTimer() {
            if (timerInterval) clearInterval(timerInterval);
            
            loginTime = Date.now();
            timerInterval = setInterval(() => {
                if (!loginTime) return;
                
                const elapsed = Date.now() - loginTime;
                const remaining = Math.max(0, 10000 - elapsed); // 10 seconds
                
                const minutes = Math.floor(remaining / 60000);
                const seconds = Math.floor((remaining % 60000) / 1000);
                
                document.getElementById('timerDisplay').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                if (remaining <= 0) {
                    clearInterval(timerInterval);
                    loginTime = null;
                }
            }, 100);
        }

        function stopTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
            loginTime = null;
            document.getElementById('timerDisplay').textContent = '00:00';
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Wait for auth system
            setTimeout(() => {
                updateLoginStatus();
                
                // Listen for auth changes
                document.addEventListener('authStateChanged', () => {
                    updateLoginStatus();
                });
            }, 500);

            // Login button - tạo session 10 giây
            document.getElementById('loginBtn').addEventListener('click', () => {
                const testUser = {
                    id: 999,
                    full_name: 'Test User',
                    email: '<EMAIL>',
                    phone: '0123456789'
                };
                
                // Mock token với thời hạn 10 giây
                const tokenExpiry = Date.now() + 10000; // 10 seconds
                
                localStorage.setItem('user', JSON.stringify(testUser));
                localStorage.setItem('userData', JSON.stringify(testUser));
                localStorage.setItem('token', 'mock_token_' + Date.now());
                localStorage.setItem('refreshToken', 'mock_refresh_token_' + Date.now());
                localStorage.setItem('tokenExpiry', tokenExpiry.toString());
                
                if (window.auth) {
                    window.auth.isAuthenticated = true;
                    window.auth.user = testUser;
                    window.auth.token = 'mock_token_' + Date.now();
                    window.auth.refreshToken = 'mock_refresh_token_' + Date.now();
                    window.auth.tokenExpiry = tokenExpiry;
                    
                    // Sửa thời gian cho test nhanh
                    window.auth.INACTIVITY_TIMEOUT = 10000; // 10 giây
                    window.auth.WARNING_TIME = 5000; // 5 giây
                    
                    // Start tracking
                    window.auth.startActivityTracking();
                    window.auth.startSessionCheck();
                    
                    window.auth.broadcastAuthChange('login', testUser);
                }
                
                updateLoginStatus();
                startTimer();
                console.log('✅ Đăng nhập test - Token hết hạn sau 10 giây');
            });

            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', () => {
                if (window.auth) {
                    window.auth.logout();
                }
                updateLoginStatus();
                stopTimer();
                console.log('✅ Đăng xuất thành công');
            });

            // Test warning dialog
            document.getElementById('testWarning').addEventListener('click', () => {
                console.log('🟡 Test Warning Dialog');
                if (window.auth && window.auth.showSessionWarning) {
                    window.auth.showSessionWarning();
                } else {
                    alert('Auth system chưa sẵn sàng');
                }
            });

            // Test expired dialog
            document.getElementById('testExpired').addEventListener('click', () => {
                console.log('🔴 Test Expired Dialog');
                if (window.auth && window.auth.showSessionExpiredDialog) {
                    window.auth.showSessionExpiredDialog();
                } else {
                    alert('Auth system chưa sẵn sàng');
                }
            });
        });
    </script>
</body>
</html>
