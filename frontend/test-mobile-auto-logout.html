<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#e53e3e">
    <title>Test Auto Logout Mobile - Nhà Hàng Ẩm Thực Phương Nam</title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <link rel="apple-touch-icon" href="img/logoPN.png">
    
    <!-- Mobile-first CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            color: #333;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }
        
        .status-item {
            padding: 0.75rem;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            font-size: 0.9rem;
        }
        
        .status-success { border-color: #10b981; background: #f0fdf4; }
        .status-warning { border-color: #f59e0b; background: #fffbeb; }
        .status-error { border-color: #ef4444; background: #fef2f2; }
        
        .countdown {
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .countdown-timer {
            font-size: 3rem;
            font-weight: bold;
            color: #e53e3e;
            font-family: 'Courier New', monospace;
            margin-bottom: 0.5rem;
        }
        
        .countdown-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 0.5rem;
            transition: all 0.2s;
        }
        
        .btn:active {
            transform: scale(0.98);
        }
        
        .btn-primary { background: #e53e3e; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.25rem;
            color: #374151;
        }
        
        .input-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #e53e3e;
        }
        
        .logs {
            background: #1f2937;
            color: #10b981;
            padding: 1rem;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .icon {
            width: 1.2rem;
            height: 1.2rem;
        }
        
        .activity-indicator {
            position: fixed;
            top: 1rem;
            right: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
            z-index: 1000;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .warning-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            text-align: center;
            max-width: 90vw;
        }
        
        .warning-popup h3 {
            color: #f59e0b;
            margin-bottom: 1rem;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
    </style>
</head>
<body>
    <!-- Activity Indicator -->
    <div id="activityIndicator" class="activity-indicator" style="display: none;"></div>
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🕐 Test Auto Logout Mobile</h1>
            <p>Tự động đăng xuất sau 2 phút không hoạt động</p>
        </div>

        <!-- Login Card -->
        <div class="card">
            <div class="section-title">
                🔐 Đăng Nhập Test
            </div>
            
            <div id="loginForm">
                <div class="input-group">
                    <label>Email:</label>
                    <input type="email" id="email" value="<EMAIL>">
                </div>
                <div class="input-group">
                    <label>Password:</label>
                    <input type="password" id="password" value="123456">
                </div>
                <button onclick="testLogin()" class="btn btn-primary">
                    🔑 Đăng Nhập Test
                </button>
            </div>

            <div id="loginStatus"></div>
        </div>

        <!-- Status Card -->
        <div class="card">
            <div class="section-title">
                📊 Trạng Thái Hệ Thống
            </div>
            
            <div class="status-grid">
                <div id="authStatus" class="status-item">
                    <strong>Đăng nhập:</strong> <span id="authStatusText">Chưa đăng nhập</span>
                </div>
                <div id="tokenStatus" class="status-item">
                    <strong>Token:</strong> <span id="tokenStatusText">Không có</span>
                </div>
                <div id="activityStatus" class="status-item">
                    <strong>Hoạt động:</strong> <span id="activityStatusText">Không theo dõi</span>
                </div>
            </div>
        </div>

        <!-- Countdown Card -->
        <div class="card">
            <div class="section-title">
                ⏱️ Đếm Ngược Auto Logout
            </div>
            
            <div class="countdown">
                <div id="countdown" class="countdown-timer">--:--</div>
                <div class="countdown-label">Thời gian còn lại trước khi tự động đăng xuất</div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="card">
            <div class="section-title">
                🎮 Thao Tác Test
            </div>
            
            <button onclick="simulateActivity()" class="btn btn-success">
                🖱️ Mô Phỏng Hoạt Động
            </button>
            <button onclick="forceLogout()" class="btn btn-danger">
                🚪 Đăng Xuất Ngay
            </button>
            <button onclick="clearAllData()" class="btn btn-secondary">
                🗑️ Xóa Tất Cả Dữ Liệu
            </button>
        </div>

        <!-- Logs Card -->
        <div class="card">
            <div class="section-title">
                📝 Logs
            </div>
            
            <div id="logs" class="logs">
                <div>🚀 Mobile Auto Logout Test initialized...</div>
            </div>
            
            <button onclick="clearLogs()" class="btn btn-secondary" style="margin-top: 0.5rem;">
                Clear Logs
            </button>
        </div>
    </div>

    <!-- Include Auth System -->
    <script src="js/auth.js"></script>

    <script>
        let countdownInterval;
        let statusInterval;
        let lastActivity = Date.now();

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Mobile page loaded, initializing test...');
            updateStatus();
            startStatusMonitoring();
            setupActivityTracking();
        });

        // Log function
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        // Setup activity tracking
        function setupActivityTracking() {
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'touchmove'];
            
            events.forEach(event => {
                document.addEventListener(event, () => {
                    lastActivity = Date.now();
                    showActivityIndicator();
                }, true);
            });
        }

        // Show activity indicator
        function showActivityIndicator() {
            const indicator = document.getElementById('activityIndicator');
            indicator.style.display = 'block';
            
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 1000);
        }

        // Test login
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            log(`🔐 Attempting mobile login with ${email}...`);
            
            try {
                const result = await auth.login(email, password);
                log('✅ Mobile login successful!');
                log(`🔑 Token expires at: ${new Date(result.tokenExpiry)}`);
                log('⏰ Inactivity logout: 2 minutes');
                updateStatus();
                startInactivityCountdown();
            } catch (error) {
                log(`❌ Mobile login failed: ${error.message}`);
                updateStatus();
            }
        }

        // Update status display
        function updateStatus() {
            const authStatusEl = document.getElementById('authStatus');
            const authStatusText = document.getElementById('authStatusText');
            const tokenStatusText = document.getElementById('tokenStatusText');
            const activityStatusText = document.getElementById('activityStatusText');

            if (auth.isAuthenticated) {
                authStatusEl.className = 'status-item status-success';
                authStatusText.textContent = `Đã đăng nhập (${auth.user?.email || 'Unknown'})`;
                tokenStatusText.textContent = 'Có token hợp lệ';
                activityStatusText.textContent = 'Đang theo dõi hoạt động';
            } else {
                authStatusEl.className = 'status-item status-error';
                authStatusText.textContent = 'Chưa đăng nhập';
                tokenStatusText.textContent = 'Không có';
                activityStatusText.textContent = 'Không theo dõi';
            }
        }

        // Start inactivity countdown
        function startInactivityCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }

            if (!auth.isAuthenticated) {
                document.getElementById('countdown').textContent = '--:--';
                return;
            }

            countdownInterval = setInterval(() => {
                if (!auth.isAuthenticated) {
                    clearInterval(countdownInterval);
                    document.getElementById('countdown').textContent = '--:--';
                    return;
                }

                const now = Date.now();
                const timeSinceActivity = now - lastActivity;
                const timeLeft = auth.INACTIVITY_TIMEOUT - timeSinceActivity;

                if (timeLeft <= 0) {
                    document.getElementById('countdown').textContent = '00:00';
                    log('⏰ Inactivity timeout reached - auto logout!');
                    auth.autoLogout();
                    clearInterval(countdownInterval);
                    updateStatus();
                    return;
                }

                // Show warning at 30 seconds
                if (timeLeft <= 30000 && timeLeft > 29000) {
                    showWarningPopup();
                }

                const minutes = Math.floor(timeLeft / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);
                document.getElementById('countdown').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // Show warning popup
        function showWarningPopup() {
            const overlay = document.createElement('div');
            overlay.className = 'overlay';
            
            const popup = document.createElement('div');
            popup.className = 'warning-popup';
            popup.innerHTML = `
                <h3>⚠️ Cảnh Báo</h3>
                <p>Bạn sẽ bị đăng xuất sau 30 giây do không hoạt động!</p>
                <button onclick="continueSession()" class="btn btn-primary" style="margin-top: 1rem;">
                    Tiếp Tục Phiên
                </button>
            `;
            
            document.body.appendChild(overlay);
            document.body.appendChild(popup);
            
            // Auto close after 30 seconds
            setTimeout(() => {
                if (document.body.contains(overlay)) {
                    document.body.removeChild(overlay);
                    document.body.removeChild(popup);
                }
            }, 30000);
        }

        // Continue session
        function continueSession() {
            lastActivity = Date.now();
            log('🔄 Session continued by user');
            
            // Remove popup
            const overlay = document.querySelector('.overlay');
            const popup = document.querySelector('.warning-popup');
            if (overlay) document.body.removeChild(overlay);
            if (popup) document.body.removeChild(popup);
        }

        // Start status monitoring
        function startStatusMonitoring() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }

            statusInterval = setInterval(() => {
                updateStatus();
                
                // Check if user was logged out
                if (!auth.isAuthenticated && countdownInterval) {
                    clearInterval(countdownInterval);
                    document.getElementById('countdown').textContent = '--:--';
                    log('🚪 User was automatically logged out!');
                }
            }, 1000);
        }

        // Simulate activity
        function simulateActivity() {
            lastActivity = Date.now();
            showActivityIndicator();
            log('🖱️ Activity simulated - timer reset');
        }

        // Force logout
        function forceLogout() {
            if (auth.isAuthenticated) {
                auth.logout();
                log('🚪 Forced logout');
                updateStatus();
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                    document.getElementById('countdown').textContent = '--:--';
                }
            } else {
                log('⚠️ Already logged out');
            }
        }

        // Clear all data
        function clearAllData() {
            auth.clearAuthData();
            log('🗑️ All auth data cleared');
            updateStatus();
            if (countdownInterval) {
                clearInterval(countdownInterval);
                document.getElementById('countdown').textContent = '--:--';
            }
        }

        // Clear logs
        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div>🚀 Mobile logs cleared...</div>';
        }

        // Listen for auth changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'user' || e.key === 'token') {
                log('📡 Auth state changed (storage event)');
                updateStatus();
            }
        });

        // Reset activity on any interaction
        document.addEventListener('touchstart', () => {
            lastActivity = Date.now();
        });

        document.addEventListener('touchmove', () => {
            lastActivity = Date.now();
        });
    </script>
</body>
</html>
