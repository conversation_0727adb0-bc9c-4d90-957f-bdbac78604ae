<!-- Header Component -->
<header class="bg-white shadow-md">
    <div class="container mx-auto px-4 py-2">
        <div class="flex justify-between items-center">           
            <div class="flex items-center">
                <img src="./img/logoPN.png" alt="Logo Nhà Hàng" class="h-16 mr-4 rounded-md border border-gray-300">
                <div class="text-2xl font-bold text-primary mr-2">
                    <span class="font-cursive">Phương Nam</span>
                </div>
                <div class="text-sm">
                    <div>Ẩm Thực</div>
                </div>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex space-x-8">
                <a href="Index-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="Index">Trang Chủ</a>
                <a href="gioithieu-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="gioithieu">Gi<PERSON><PERSON></a>
                <a href="Menu-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="Menu">Thực Đơn</a>
                <a href="lienhe&datban-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="lienhe&datban">Đặt Bàn & Liên Hệ</a>
            </nav>

            <div class="flex items-center">
                <div id="userDisplay" class="hidden mr-4">
                    <span class="text-gray-700 mr-2">Xin chào,</span>
                    <span id="userName" class="font-semibold text-primary"></span>
                </div>



                <!-- Invoice History Button - Only visible when logged in -->
                <button id="invoiceHistoryBtn" class="hidden bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary hover:text-white transition duration-300 mr-3" onclick="window.location.href='DanhSachHoaDon.html'">
                    <i class="fas fa-receipt mr-2"></i>Lịch sử đơn hàng
                </button>



                <button id="loginBtn" class="md:block bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary transition duration-300 mr-3">
                    Đăng Nhập
                </button>
                <button id="logoutBtn" class="hidden bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary  transition duration-300 mr-3">
                    Đăng Xuất
                </button>

                <!-- Cart Button - Only visible on menu page -->
                <button id="cartBtn" class="hidden relative mr-3 p-2 rounded-lg hover:bg-gray-100 transition duration-300 group">
                    <i class="fas fa-shopping-cart text-gray-600 group-hover:text-primary text-xl transition duration-300"></i>
                    <span id="cartCounter" class="absolute -top-1 -right-1 bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold shadow-lg transform scale-0 transition-transform duration-300">0</span>
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                        Giỏ hàng
                    </div>
                </button>

                <button id="menuToggle" class="md:hidden ml-4 focus:outline-none">
                    <i class="fas fa-bars text-2xl text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <nav id="mobileNav" class="md:hidden hidden mt-4 pb-4">
            <div class="flex flex-col space-y-3">
                <a href="Index-new.html" class="mobile-nav-link py-2 px-4 rounded hover:bg-gray-100" data-page="Index">Trang Chủ</a>
                <a href="gioithieu-new.html" class="mobile-nav-link py-2 px-4 rounded hover:bg-gray-100" data-page="gioithieu">Giới Thiệu</a>
                <a href="Menu-new.html" class="mobile-nav-link py-2 px-4 rounded hover:bg-gray-100" data-page="Menu">Thực Đơn</a>
                <a href="lienhe&datban-new.html" class="mobile-nav-link py-2 px-4 rounded hover:bg-gray-100" data-page="lienhe&datban">Đặt Bàn & Liên Hệ</a>

                <!-- Mobile Cart Button - Only visible on menu page -->
                <div id="mobileCartContainer" class="hidden px-4 py-2">
                    <button id="mobileCartBtn" class="w-full bg-primary hover:bg-red-700 text-white py-2 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Giỏ hàng (<span id="mobileCartCounter">0</span>)
                    </button>
                </div>



                <!-- Mobile Invoice History Button - Only visible when logged in -->
                <div id="mobileInvoiceHistoryContainer" class="hidden px-4 py-2">
                    <button onclick="window.location.href='DanhSachHoaDon.html'" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded-lg transition duration-300 flex items-center justify-center">
                        <i class="fas fa-receipt mr-2"></i>
                        Lịch sử đơn hàng
                    </button>
                </div>

                <div class="border-t border-gray-200 pt-3">
                    <div id="mobileUserDisplay" class="hidden mb-2 px-4">
                        <span class="text-gray-700">Xin chào, </span>
                        <span id="mobileUserName" class="font-semibold text-primary"></span>
                    </div>



                    <button id="mobileLoginBtn" class="text-left py-2 px-4 rounded hover:bg-gray-100 w-full">Đăng Nhập</button>
                    <button id="mobileLogoutBtn" class="hidden text-left py-2 px-4 rounded hover:bg-gray-100 w-full">Đăng Xuất</button>
                </div>
            </div>
        </nav>
    </div>
</header>
