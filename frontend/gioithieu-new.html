<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gi<PERSON><PERSON> - <PERSON> Ẩm <PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">

    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Dancing+Script:wght@600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">


    <!-- Custom CSS -->
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    
    <!-- External JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    
    <!-- Page specific styles -->
    <style>
        .custom-title-h2 {
            font-family: 'Playfair Display', cursive;
        }
        
        /* Video grid responsive */
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            padding: 2rem 0;
        }
        
        /* Team member cards */
        .team-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .team-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        /* About sections */
        .about-section {
            margin-bottom: 4rem;
        }
        
        .about-section:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <!-- Ad Banner Placeholder -->
    <div id="ad-banner-placeholder"></div>

    <!-- Main Content -->
    <main>
        <!-- About Section Content -->
        <section class="py-12 bg-white about-section" id="aboutSection">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12 fade-in" id="aboutTitle">
                    <h2 class="text-3xl font-bold mb-2">Giới Thiệu</h2>
                    <p class="text-yellow-700">Câu chuyện về Nhà hàng Ẩm thực Phương Nam</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center mb-16 bg-gray-50 p-8 rounded-lg slide-in-left" id="aboutJourney">
                    <div class="bg-gray-300 h-80 md:h-96 rounded-lg flex items-center justify-center overflow-hidden hover-scale">
                        <img src="img/gioithieu3.jpg" alt="Nhà hàng Phương Nam" class="w-full h-full object-cover cursor-zoom-in" onclick="showImage(this.src)">
                    </div>
                    
                    <div class="slide-in-right">
                        <h3 class="text-2xl font-bold mb-4">Hành Trình Của Chúng Tôi</h3>
                        <p class="text-gray-600 mb-4">Nhà hàng Ẩm thực Phương Nam được thành lập vào năm 2010 với mong muốn mang đến cho thực khách những trải nghiệm ẩm thực Nam Bộ đích thực nhất.</p>
                        <p class="text-gray-600 mb-4">Từ một quán nhỏ với vài món đặc sản, chúng tôi đã dần phát triển và trở thành điểm đến quen thuộc cho những ai yêu thích ẩm thực miền Nam, đặc biệt là các món ăn dân dã, đậm đà hương vị quê hương.</p>
                        <p class="text-gray-600">Đến nay, Phương Nam tự hào là một trong những nhà hàng hàng đầu về ẩm thực Nam Bộ, nơi không chỉ mang đến những món ăn ngon mà còn là nơi lưu giữ và phát huy những giá trị văn hóa ẩm thực truyền thống của vùng đất phương Nam trù phú.</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center mb-16 bg-red-50 p-8 rounded-lg slide-in-right" id="aboutPhilosophy">
                    
                    <div class="order-2 md:order-1 slide-in-left">
                        <h3 class="text-2xl font-bold mb-4">Triết Lý Ẩm Thực</h3>
                        <p class="text-gray-600 mb-4">Tại Phương Nam, chúng tôi tin rằng ẩm thực không chỉ đơn thuần là việc thưởng thức món ăn, mà còn là cách để kết nối con người với nhau và với văn hóa bản địa.</p>
                        <p class="text-gray-600 mb-4">Mỗi món ăn tại Phương Nam đều được chế biến cẩn thận từ những nguyên liệu tươi ngon nhất, theo công thức truyền thống được giữ gìn và phát triển qua nhiều thế hệ.</p>
                        <p class="text-gray-600">Chúng tôi tôn trọng bản sắc của từng món ăn, đồng thời không ngừng sáng tạo để mang đến những trải nghiệm ẩm thực mới mẻ nhưng vẫn giữ được hồn cốt của ẩm thực Nam Bộ.</p>
                    </div>
                    <div class="order-1 md:order-2 bg-gray-300 h-80 md:h-96 rounded-lg flex items-center justify-center hover-scale">
                        <img src="img/hinhtruyenthong2.jpg" alt="Nhà hàng Phương Nam" class="w-full h-full object-cover rounded-lg cursor-zoom-in" onclick="showImage(this.src)">
                    </div>
                </div>

                <div class="text-center mb-12 bg-yellow-50 p-8 rounded-lg fade-in" id="aboutTeamTitle">
                    <h3 class="text-2xl font-bold mb-4">Đội Ngũ Của Chúng Tôi</h3>
                    <p class="text-gray-600 mb-8">Đằng sau mỗi món ăn ngon là những con người tài năng và đam mê</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12" id="aboutTeam">
                    <!-- Chef 1 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-md text-center team-card stagger-item">
                        <div class="h-64 overflow-hidden">
                            <img src="img/truong.jpg" alt="Nguyễn Nhật Trường" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h4 class="font-bold text-xl mb-1">Nguyễn Nhật Trường</h4>
                            <p class="text-primary mb-2">Bếp Trưởng</p>
                            <p class="text-gray-600">Với hơn 20 năm kinh nghiệm trong ẩm thực Nam Bộ, Chef Trường là linh hồn của nhà hàng Phương Nam.</p>
                        </div>
                    </div>

                    <!-- Chef 2 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-md text-center team-card stagger-item">
                        <div class="h-64 overflow-hidden">
                            <img src="img/K.Thuat.jpg" alt="Nguyễn Huỳnh Kỹ Thuật" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h4 class="font-bold text-xl mb-1">Nguyễn Huỳnh Kỹ Thuật</h4>
                            <p class="text-primary mb-2">Bếp Phó</p>
                            <p class="text-gray-600">Chuyên gia về các món canh và lẩu, mang đến hương vị đặc trưng cho Phương Nam.</p>
                        </div>
                    </div>

                    <!-- Chef 3 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-md text-center team-card stagger-item">
                        <div class="h-64 overflow-hidden">
                            <img src="img/T.VY.jpg" alt="Hứa Thị Thảo Vy" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h4 class="font-bold text-xl mb-1">Hứa Thị Thảo Vy</h4>
                            <p class="text-primary mb-2">Đầu Bếp Món Nướng</p>
                            <p class="text-gray-600">Bậc thầy về các món nướng, đặc biệt là cá lóc nướng trui và các món nướng miền Tây.</p>
                        </div>
                    </div>

                    <!-- Chef 4 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-md text-center team-card stagger-item">
                        <div class="h-64 overflow-hidden">
                            <img src="img/t. vu.jpg" alt="Đỗ Thiên Vũ" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h4 class="font-bold text-xl mb-1">Đỗ Thiên Vũ</h4>
                            <p class="text-primary mb-2">Chuyên Gia Món Chay</p>
                            <p class="text-gray-600">Người sáng tạo ra nhiều món chay độc đáo mang hương vị đặc trưng phương Nam.</p>
                        </div>
                    </div>
                </div>

                <!-- Restaurant Photos -->
                <div class="text-center mb-12 bg-green-50 p-8 rounded-lg scale-in" id="aboutSpaceTitle">
                    <h3 class="text-2xl font-bold mb-4">Không Gian Nhà Hàng</h3>
                    <p class="text-gray-600 mb-8">Khung cảnh ấm cúng và sang trọng tại Phương Nam</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 bg-blue-50 p-8 rounded-lg fade-in" id="aboutSpace">
                    <!-- Không gian chung -->
                    <div class="h-64 rounded-lg overflow-hidden shadow-md hover-lift">
                        <img src="img/Alo.jpg" alt="Không gian nhà hàng" class="w-full h-full object-cover cursor-zoom-in" onclick="showImage(this.src)">
                    </div>

                    <!-- Khu vực VIP -->
                    <div class="h-64 rounded-lg overflow-hidden shadow-md hover-lift">
                        <img src="img/alo2.jpg" alt="Khu vực VIP" class="w-full h-full object-cover cursor-zoom-in" onclick="showImage(this.src)">
                    </div>

                    <!-- Sân vườn -->
                    <div class="h-64 rounded-lg overflow-hidden shadow-md hover-lift">
                        <img src="img/alo3.jpg" alt="Sân vườn" class="w-full h-full object-cover cursor-zoom-in" onclick="showImage(this.src)">
                    </div>
                </div>
            </div>
        </section>

        <!-- Video Review Section -->
        <section class="py-12 bg-gray-50 about-section">
            <div class="container mx-auto px-4">
                <div class="text-center mb-8 fade-in">
                    <h2 class="custom-title-h2 text-4xl mb-4 font-bold text-amber-800">
                        <span>VIDEO ĐƯỢC CÁC TIKTOKER REVIEW</span>
                    </h2>
                </div>
                
                <!-- Video Grid -->
                <div class="video-grid">
                    <!-- Video 1 -->
                    <div class="bg-white shadow-lg rounded-lg overflow-hidden hover:shadow-2xl transition-shadow duration-300 stagger-item">
                        <video controls class="w-full">
                            <source src="img/clip1.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="p-4">
                            <p class="text-sm text-gray-600">Tiktoker Nguyễn Thị Mộng Nghi review, ngày 18/8/2023.</p>
                        </div>
                    </div>

                    <!-- Video 2 -->
                    <div class="bg-white shadow-lg rounded-lg overflow-hidden hover:shadow-2xl transition-shadow duration-300 stagger-item">
                        <video controls class="w-full">
                            <source src="img/clip2.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="p-4">
                            <p class="text-sm text-gray-600">Tiktoker Nguyễn Thị Mộng Nghi review, ngày 28/2/2024.</p>
                        </div>
                    </div>

                    <!-- Video 3 -->
                    <div class="bg-white shadow-lg rounded-lg overflow-hidden hover:shadow-2xl transition-shadow duration-300 stagger-item">
                        <video controls class="w-full">
                            <source src="img/clip3.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="p-4">
                            <p class="text-sm text-gray-600">Tiktoker Nguyễn Thị Mộng Nghi review, ngày 28/2/2024.</p>
                        </div>
                    </div>

                    <!-- Video 4 -->
                    <div class="bg-white shadow-lg rounded-lg overflow-hidden hover:shadow-2xl transition-shadow duration-300 stagger-item">
                        <video controls class="w-full">
                            <source src="img/clip4.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="p-4">
                            <p class="text-sm text-gray-600">Tiktoker Ghiền review, ngày 12/1/2024.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal xem ảnh to -->
    <div id="imageModal" class="fixed inset-0 z-[9999] bg-black bg-opacity-80 hidden flex items-center justify-center p-4">
        <div class="relative w-auto max-w-5xl mx-auto">
            <img id="modalImage" class="w-full h-auto max-h-[90vh] object-contain rounded-lg shadow-lg" />
            <button onclick="hideImage()" class="absolute top-2 right-2 text-white text-3xl font-bold bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75 transition">
            &times;
            </button>
        </div>
    </div>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <!-- Chatbot Placeholder -->
    <div id="chatbot-placeholder"></div>

    <!-- Login Modal Placeholder -->
    <div id="login-modal-placeholder"></div>

    <!-- Floating Contact Placeholder -->
    <div id="floating-contact-placeholder"></div>

    <!-- JavaScript -->
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
    <script src="js/chatbot.js"></script>
    
    <!-- Initialize app after all scripts loaded -->
    <script>
        // Ensure proper initialization order
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Gioithieu-new.html loaded successfully!');
            
            // Setup GSAP animations for about page
            if (window.gsap && window.ScrollTrigger) {
                // About sections animation
                gsap.utils.toArray(".about-section").forEach((section, i) => {
                    gsap.from(section, {
                        opacity: 0,
                        y: 50,
                        duration: 1,
                        delay: i * 0.2,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 80%",
                            toggleActions: "play none none reset"
                        }
                    });
                });

                // Team cards stagger animation
                gsap.utils.toArray(".team-card").forEach((card, i) => {
                    gsap.from(card, {
                        opacity: 0,
                        y: 60,
                        duration: 0.8,
                        delay: i * 0.15,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: card,
                            start: "top 85%",
                            toggleActions: "play none none reset"
                        }
                    });
                });

                // Video grid animation
                gsap.utils.toArray(".video-grid > div").forEach((video, i) => {
                    gsap.from(video, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.6,
                        delay: i * 0.1,
                        ease: "back.out(1.7)",
                        scrollTrigger: {
                            trigger: video,
                            start: "top 85%",
                            toggleActions: "play none none reset"
                        }
                    });
                });
            }
        });
    </script>

    <script>
        function showImage(src) {
        const modal = document.getElementById("imageModal");
        const modalImg = document.getElementById("modalImage");
        modalImg.src = src;
        modal.classList.remove("hidden");
        document.body.classList.add("overflow-hidden"); // chặn cuộn nền
    }

    function hideImage() {
        document.getElementById("imageModal").classList.add("hidden");
        document.body.classList.remove("overflow-hidden"); // cho cuộn lại
    }
    </script>
</body>
</html>
