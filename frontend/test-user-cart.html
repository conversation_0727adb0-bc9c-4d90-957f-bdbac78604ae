<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Cart System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">Test User Cart System</h1>
        
        <!-- User Login Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">User Login Simulation</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="loginUser('user1')" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Login as User 1
                </button>
                <button onclick="loginUser('user2')" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Login as User 2
                </button>
                <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    Logout (Guest)
                </button>
            </div>
            <div class="mt-4 p-3 bg-gray-50 rounded">
                <strong>Current User:</strong> <span id="currentUser">guest</span>
            </div>
        </div>

        <!-- Cart Actions -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Cart Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="addTestItem()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Add Test Item
                </button>
                <button onclick="viewCart()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                    View Cart
                </button>
                <button onclick="clearCart()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    Clear Cart
                </button>
                <button onclick="debugInfo()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Debug Info
                </button>
            </div>
        </div>

        <!-- Cart Display -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Current Cart</h2>
            <div id="cartDisplay" class="min-h-20 p-4 bg-gray-50 rounded">
                <p class="text-gray-500">Cart will be displayed here...</p>
            </div>
        </div>

        <!-- All User Carts -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">All User Carts</h2>
            <div id="allCartsDisplay" class="min-h-20 p-4 bg-gray-50 rounded">
                <p class="text-gray-500">All user carts will be displayed here...</p>
            </div>
        </div>
    </div>

    <!-- Load Cart System -->
    <script src="js/cart.js"></script>

    <script>
        let cartManager;

        // Initialize cart manager
        document.addEventListener('DOMContentLoaded', function() {
            cartManager = new CartManager();
            updateDisplay();
        });

        // Simulate user login
        function loginUser(userId) {
            const userData = {
                id: userId,
                email: `${userId}@test.com`,
                full_name: `Test User ${userId.slice(-1)}`,
                phone: `090000000${userId.slice(-1)}`
            };

            localStorage.setItem('user', JSON.stringify(userData));
            localStorage.setItem('token', 'test-token-' + userId);

            // Trigger auth state change
            cartManager.handleUserChange();
            updateDisplay();
            
            console.log(`✅ Logged in as ${userId}`);
        }

        // Simulate logout
        function logout() {
            localStorage.removeItem('user');
            localStorage.removeItem('userData');
            localStorage.removeItem('token');

            // Trigger auth state change
            cartManager.handleUserChange();
            updateDisplay();
            
            console.log('✅ Logged out');
        }

        // Add test item to cart
        function addTestItem() {
            const testItem = {
                id_mon: Date.now(),
                ten_mon: `Test Item ${Math.floor(Math.random() * 100)}`,
                gia: Math.floor(Math.random() * 100000) + 10000,
                hinh_anh: 'img/placeholder.png',
                mo_ta: 'Test item description',
                so_luong: 999,
                category: 'test'
            };

            cartManager.addToCart(testItem);
            updateDisplay();
        }

        // View current cart
        function viewCart() {
            const cart = cartManager.getCart();
            console.log('Current cart:', cart);
            updateDisplay();
        }

        // Clear current cart
        function clearCart() {
            cartManager.clearCart();
            updateDisplay();
        }

        // Show debug info
        function debugInfo() {
            const info = cartManager.debugCartInfo();
            console.log('Debug info:', info);
            alert(JSON.stringify(info, null, 2));
        }

        // Update display
        function updateDisplay() {
            // Update current user
            document.getElementById('currentUser').textContent = cartManager.currentUserId;

            // Update current cart display
            const cart = cartManager.getCart();
            const cartDisplay = document.getElementById('cartDisplay');
            
            if (cart.length === 0) {
                cartDisplay.innerHTML = '<p class="text-gray-500">Cart is empty</p>';
            } else {
                cartDisplay.innerHTML = cart.map(item => `
                    <div class="flex justify-between items-center p-2 border-b">
                        <span>${item.ten_mon || item.name}</span>
                        <span>${item.qty || 1}x ${(item.gia || item.price || 0).toLocaleString()}đ</span>
                    </div>
                `).join('');
            }

            // Update all carts display
            const allCarts = cartManager.getAllUserCarts();
            const allCartsDisplay = document.getElementById('allCartsDisplay');
            
            if (Object.keys(allCarts).length === 0) {
                allCartsDisplay.innerHTML = '<p class="text-gray-500">No user carts found</p>';
            } else {
                allCartsDisplay.innerHTML = Object.entries(allCarts).map(([userId, userCart]) => `
                    <div class="mb-4 p-3 border rounded">
                        <h3 class="font-semibold text-lg mb-2">User: ${userId} (${userCart.length} items)</h3>
                        ${userCart.length === 0 ? 
                            '<p class="text-gray-500">Empty cart</p>' :
                            userCart.map(item => `
                                <div class="text-sm text-gray-600">
                                    ${item.ten_mon || item.name} - ${item.qty || 1}x ${(item.gia || item.price || 0).toLocaleString()}đ
                                </div>
                            `).join('')
                        }
                    </div>
                `).join('');
            }
        }
    </script>
</body>
</html>
