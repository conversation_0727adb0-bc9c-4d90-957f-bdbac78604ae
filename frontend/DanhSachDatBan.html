<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> Đặt <PERSON>n - Restaurant Management</title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .reservation-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .reservation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .status-pending { border-left-color: #f59e0b; }
        .status-confirmed { border-left-color: #10b981; }
        .status-cancelled { border-left-color: #ef4444; }
        
        .filter-tab {
            transition: all 0.3s ease;
        }
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }
        
        .modal-backdrop {
            backdrop-filter: blur(5px);
            background: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        /* Modern gradient background */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* Enhanced header styling */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced cards */
        .bg-white {
            background: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Enhanced scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="sticky top-0 z-40">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 w-12 h-12 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">Quản Lý Đặt Bàn</h1>
                        <p class="text-gray-600">Theo dõi và quản lý các đặt bàn</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- User Info -->
                    <div class="flex items-center space-x-3 bg-gray-50 px-4 py-2 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <div id="userRoleBadge" class="px-2 py-1 rounded text-xs font-semibold">
                                <!-- Role badge will be inserted here -->
                            </div>
                            <span id="currentUserName" class="text-sm font-medium text-gray-700">
                                <!-- Username will be inserted here -->
                            </span>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 transition-colors" title="Đăng xuất">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>

                    <!-- Navigation -->
                    <div class="flex items-center space-x-3">
                        <button onclick="goToInvoices()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-receipt mr-2"></i>Hóa đơn
                        </button>
                        <button onclick="goToMenu()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-utensils mr-2"></i>Menu
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Tổng đặt bàn</p>
                        <p id="totalReservations" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-calendar-alt text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Chờ xác nhận</p>
                        <p id="pendingReservations" class="text-2xl font-bold text-yellow-600">0</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-hourglass-half text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Đã xác nhận</p>
                        <p id="confirmedReservations" class="text-2xl font-bold text-green-600">0</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Đã hủy</p>
                        <p id="cancelledReservations" class="text-2xl font-bold text-red-600">0</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <!-- Status Filters -->
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterByStatus('all')" class="filter-tab active px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50" data-status="all">
                        <i class="fas fa-list mr-2"></i>Tất cả
                    </button>
                    <button onclick="filterByStatus('cho_xac_nhan')" class="filter-tab px-4 py-2 rounded-lg border border-gray-300 hover:bg-yellow-50" data-status="cho_xac_nhan">
                        <i class="fas fa-hourglass-half mr-2"></i>Chờ xác nhận
                    </button>
                    <button onclick="filterByStatus('da_xac_nhan')" class="filter-tab px-4 py-2 rounded-lg border border-gray-300 hover:bg-green-50" data-status="da_xac_nhan">
                        <i class="fas fa-check-circle mr-2"></i>Đã xác nhận
                    </button>
                    <button onclick="filterByStatus('da_huy')" class="filter-tab px-4 py-2 rounded-lg border border-gray-300 hover:bg-red-50" data-status="da_huy">
                        <i class="fas fa-times-circle mr-2"></i>Đã hủy
                    </button>
                </div>

                <!-- Search and Date Filter -->
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <input type="date" id="dateFilter" class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-calendar-alt absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Tìm theo tên, SĐT..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <button onclick="refreshReservations()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Làm mới
                    </button>
                </div>
            </div>
        </div>

        <!-- Reservations List -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-list mr-2 text-blue-600"></i>Danh sách đặt bàn
                </h2>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState" class="p-8 text-center">
                <div class="inline-flex items-center">
                    <i class="fas fa-spinner fa-spin text-blue-600 text-xl mr-3"></i>
                    <span class="text-gray-600">Đang tải dữ liệu...</span>
                </div>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="p-8 text-center hidden">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-calendar-times text-6xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-600 mb-2">Không có đặt bàn nào</h3>
                <p class="text-gray-500">Chưa có đặt bàn nào phù hợp với bộ lọc hiện tại</p>
            </div>

            <!-- Reservations Container -->
            <div id="reservationsContainer" class="divide-y divide-gray-200">
                <!-- Reservations will be loaded here -->
            </div>

            <!-- Pagination -->
            <div id="paginationContainer" class="p-6 border-t border-gray-200 hidden">
                <!-- Pagination will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Reservation Detail Modal -->
    <div id="reservationModal" class="fixed inset-0 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="modal-content max-w-2xl mx-4 max-h-[90vh] overflow-auto">
            <div class="p-6">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-800">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        Chi tiết đặt bàn
                    </h3>
                    <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div id="modalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/admin-auth.js"></script>
    <script>
        let adminAuth;
        let currentFilter = 'all';
        let currentPage = 1;
        let allReservations = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            adminAuth = new AdminAuth();
            
            // Check authentication
            if (!adminAuth.isLoggedIn()) {
                window.location.href = 'admin-login.html';
                return;
            }

            initializePage();
        });

        // Initialize page with authentication
        function initializePage() {
            updateUIForUserRole();
            loadReservations();
            setupEventListeners();
        }

        // Update UI based on user role
        function updateUIForUserRole() {
            const currentUser = adminAuth.getCurrentUser();
            if (!currentUser) return;

            // Update user info in header
            document.getElementById('currentUserName').textContent = currentUser.fullName;

            const roleBadge = document.getElementById('userRoleBadge');
            if (currentUser.role === 'admin') {
                roleBadge.textContent = 'ADMIN';
                roleBadge.className = 'px-2 py-1 rounded text-xs font-semibold bg-red-100 text-red-800';
            } else {
                roleBadge.textContent = 'NHÂN VIÊN';
                roleBadge.className = 'px-2 py-1 rounded text-xs font-semibold bg-blue-100 text-blue-800';
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Search input
            document.getElementById('searchInput').addEventListener('input', debounce(function() {
                loadReservations();
            }, 500));

            // Date filter
            document.getElementById('dateFilter').addEventListener('change', function() {
                loadReservations();
            });
        }

        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Load reservations from database API
        async function loadReservations() {
            showLoading(true);

            try {
                // Build API URL with filters
                const params = new URLSearchParams();

                // Status filter
                if (currentFilter !== 'all') {
                    params.append('status', currentFilter);
                }

                // Date filter
                const dateFilter = document.getElementById('dateFilter').value;
                if (dateFilter) {
                    params.append('date', dateFilter);
                }

                // Search filter
                const searchTerm = document.getElementById('searchInput').value.trim();
                if (searchTerm) {
                    params.append('phone', searchTerm); // API supports phone search
                }

                // Pagination
                params.append('page', currentPage);
                params.append('limit', 50);

                const apiUrl = `http://localhost:3000/api/datban?${params.toString()}`;
                console.log('🔍 Loading reservations from:', apiUrl);

                const response = await fetch(apiUrl);
                const result = await response.json();

                if (result.success) {
                    allReservations = result.data || [];
                    console.log('✅ Loaded reservations from DB:', allReservations);

                    // Update statistics
                    await updateStatistics();

                    // Render reservations
                    renderReservations(allReservations);
                } else {
                    console.error('❌ API Error:', result.message);
                    showError('Không thể tải dữ liệu đặt bàn: ' + result.message);
                    allReservations = [];
                    renderReservations([]);
                }

            } catch (error) {
                console.error('❌ Network Error:', error);
                showError('Lỗi kết nối server: ' + error.message);
                allReservations = [];
                renderReservations([]);
            }

            showLoading(false);
        }



        // Update statistics from API
        async function updateStatistics() {
            try {
                // Load all reservations for accurate statistics
                const response = await fetch('http://localhost:3000/api/datban?limit=1000');
                const result = await response.json();

                if (result.success) {
                    const allReservations = result.data || [];

                    const total = allReservations.length;
                    const pending = allReservations.filter(res => res.trang_thai === 'cho_xac_nhan').length;
                    const confirmed = allReservations.filter(res => res.trang_thai === 'da_xac_nhan').length;
                    const cancelled = allReservations.filter(res => res.trang_thai === 'da_huy').length;

                    document.getElementById('totalReservations').textContent = total;
                    document.getElementById('pendingReservations').textContent = pending;
                    document.getElementById('confirmedReservations').textContent = confirmed;
                    document.getElementById('cancelledReservations').textContent = cancelled;
                } else {
                    console.error('❌ Error loading statistics:', result.message);
                    // Set to 0 if can't load
                    document.getElementById('totalReservations').textContent = '0';
                    document.getElementById('pendingReservations').textContent = '0';
                    document.getElementById('confirmedReservations').textContent = '0';
                    document.getElementById('cancelledReservations').textContent = '0';
                }
            } catch (error) {
                console.error('❌ Network error loading statistics:', error);
                // Set to 0 if can't load
                document.getElementById('totalReservations').textContent = '0';
                document.getElementById('pendingReservations').textContent = '0';
                document.getElementById('confirmedReservations').textContent = '0';
                document.getElementById('cancelledReservations').textContent = '0';
            }
        }

        // Render reservations list
        function renderReservations(reservations) {
            const container = document.getElementById('reservationsContainer');
            const emptyState = document.getElementById('emptyState');

            if (reservations.length === 0) {
                container.innerHTML = '';
                emptyState.classList.remove('hidden');
                return;
            }

            emptyState.classList.add('hidden');

            const reservationsHTML = reservations.map(reservation => {
                const statusClass = getStatusClass(reservation.trang_thai);
                const statusText = getStatusText(reservation.trang_thai);
                const statusIcon = getStatusIcon(reservation.trang_thai);

                return `
                    <div class="reservation-card ${statusClass} p-6 hover:bg-gray-50 cursor-pointer" onclick="showReservationDetail(${reservation.id_datban})">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-4 mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-gray-900">${reservation.ten_khach}</h3>
                                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                                            <span><i class="fas fa-phone mr-1"></i>${reservation.sdt}</span>
                                            ${reservation.email ? `<span><i class="fas fa-envelope mr-1"></i>${reservation.email}</span>` : ''}
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-calendar-alt mr-2 text-blue-500"></i>
                                        <span>${formatDate(reservation.ngay)}</span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-clock mr-2 text-green-500"></i>
                                        <span>${formatTime(reservation.gio)}</span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-users mr-2 text-purple-500"></i>
                                        <span>${reservation.so_luong_khach} người</span>
                                    </div>
                                    <div class="flex items-center text-gray-600">
                                        <i class="fas fa-clock mr-2 text-gray-400"></i>
                                        <span>${formatDateTime(reservation.created_at)}</span>
                                    </div>
                                </div>

                                ${reservation.ghi_chu ? `
                                    <div class="mt-3 p-3 bg-gray-50 rounded-lg">
                                        <p class="text-sm text-gray-600">
                                            <i class="fas fa-sticky-note mr-2 text-yellow-500"></i>
                                            ${reservation.ghi_chu}
                                        </p>
                                    </div>
                                ` : ''}
                            </div>

                            <div class="flex-shrink-0 ml-6">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusClass}">
                                    <i class="fas ${statusIcon} mr-2"></i>
                                    ${statusText}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = reservationsHTML;
        }

        // Utility functions
        function getStatusClass(status) {
            switch (status) {
                case 'cho_xac_nhan': return 'status-pending bg-yellow-50 text-yellow-800';
                case 'da_xac_nhan': return 'status-confirmed bg-green-50 text-green-800';
                case 'da_huy': return 'status-cancelled bg-red-50 text-red-800';
                default: return 'bg-gray-50 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'cho_xac_nhan': return 'Chờ xác nhận';
                case 'da_xac_nhan': return 'Đã xác nhận';
                case 'da_huy': return 'Đã hủy';
                default: return 'Không xác định';
            }
        }

        function getStatusIcon(status) {
            switch (status) {
                case 'cho_xac_nhan': return 'fa-hourglass-half';
                case 'da_xac_nhan': return 'fa-check-circle';
                case 'da_huy': return 'fa-times-circle';
                default: return 'fa-question-circle';
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        }

        function formatTime(timeString) {
            return timeString.substring(0, 5); // HH:MM
        }

        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Filter functions
        function filterByStatus(status) {
            currentFilter = status;
            currentPage = 1; // Reset to first page

            // Update active filter button
            document.querySelectorAll('.filter-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-status="${status}"]`).classList.add('active');

            // Reload reservations with new filter
            loadReservations();
        }

        // Show/hide loading state
        function showLoading(show) {
            const loadingState = document.getElementById('loadingState');
            const container = document.getElementById('reservationsContainer');

            if (show) {
                loadingState.classList.remove('hidden');
                container.innerHTML = '';
            } else {
                loadingState.classList.add('hidden');
            }
        }

        // Show reservation detail modal
        function showReservationDetail(reservationId) {
            const reservation = allReservations.find(res => res.id_datban === reservationId);
            if (!reservation) return;

            const modalContent = document.getElementById('modalContent');
            const statusClass = getStatusClass(reservation.trang_thai);
            const statusText = getStatusText(reservation.trang_thai);
            const statusIcon = getStatusIcon(reservation.trang_thai);

            modalContent.innerHTML = `
                <div class="space-y-6">
                    <!-- Status Badge -->
                    <div class="text-center">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium ${statusClass}">
                            <i class="fas ${statusIcon} mr-2"></i>
                            ${statusText}
                        </span>
                    </div>

                    <!-- Customer Information -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-user mr-2 text-blue-600"></i>
                            Thông tin khách hàng
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Họ tên</label>
                                <p class="text-gray-900 font-medium">${reservation.ten_khach}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Số điện thoại</label>
                                <p class="text-gray-900 font-medium">${reservation.sdt}</p>
                            </div>
                            ${reservation.email ? `
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-600 mb-1">Email</label>
                                    <p class="text-gray-900 font-medium">${reservation.email}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- Reservation Details -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-calendar-check mr-2 text-green-600"></i>
                            Chi tiết đặt bàn
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Ngày</label>
                                <p class="text-gray-900 font-medium">${formatDate(reservation.ngay)}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Giờ</label>
                                <p class="text-gray-900 font-medium">${formatTime(reservation.gio)}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Số lượng khách</label>
                                <p class="text-gray-900 font-medium">${reservation.so_luong_khach} người</p>
                            </div>
                        </div>

                        ${reservation.ghi_chu ? `
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-600 mb-1">Ghi chú</label>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-gray-900">${reservation.ghi_chu}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>

                    <!-- System Information -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-info-circle mr-2 text-purple-600"></i>
                            Thông tin hệ thống
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Mã đặt bàn</label>
                                <p class="text-gray-900 font-medium">#${reservation.id_datban.toString().padStart(6, '0')}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Thời gian tạo</label>
                                <p class="text-gray-900 font-medium">${formatDateTime(reservation.created_at)}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">Cập nhật lần cuối</label>
                                <p class="text-gray-900 font-medium">${formatDateTime(reservation.updated_at)}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons (Admin Only) -->
                    ${adminAuth.isAdmin() ? `
                        <div class="flex justify-center space-x-4 pt-4 border-t border-gray-200">
                            ${reservation.trang_thai === 'cho_xac_nhan' ? `
                                <button onclick="updateReservationStatus(${reservation.id_datban}, 'da_xac_nhan')"
                                        class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-check mr-2"></i>Xác nhận
                                </button>
                                <button onclick="updateReservationStatus(${reservation.id_datban}, 'da_huy')"
                                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-times mr-2"></i>Hủy bỏ
                                </button>
                            ` : ''}
                            ${reservation.trang_thai === 'da_xac_nhan' ? `
                                <button onclick="updateReservationStatus(${reservation.id_datban}, 'da_huy')"
                                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-times mr-2"></i>Hủy bỏ
                                </button>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>
            `;

            document.getElementById('reservationModal').classList.remove('hidden');
        }

        // Close modal
        function closeModal() {
            document.getElementById('reservationModal').classList.add('hidden');
        }

        // Update reservation status (Admin only)
        async function updateReservationStatus(reservationId, newStatus) {
            if (!adminAuth.isAdmin()) {
                alert('Bạn không có quyền thực hiện thao tác này!');
                return;
            }

            const actionText = newStatus === 'da_xac_nhan' ? 'xác nhận' : 'hủy';

            if (confirm(`Bạn có chắc chắn muốn ${actionText} đặt bàn này?`)) {
                try {
                    showLoading(true);

                    // Call API to update status
                    const response = await fetch(`http://localhost:3000/api/datban/${reservationId}/status`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            trang_thai: newStatus
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Refresh display
                        await loadReservations();
                        closeModal();
                        showSuccess(`✅ Đã ${actionText} đặt bàn thành công!`);
                    } else {
                        showError('❌ Lỗi: ' + result.message);
                    }

                } catch (error) {
                    console.error('❌ Error updating status:', error);
                    showError('❌ Lỗi kết nối: ' + error.message);
                } finally {
                    showLoading(false);
                }
            }
        }

        // Navigation functions
        function goToInvoices() {
            window.location.href = 'DanhSachHoaDon.html';
        }

        function goToMenu() {
            window.location.href = 'menu.html';
        }

        function logout() {
            if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
                adminAuth.logout();
            }
        }

        function refreshReservations() {
            loadReservations();
        }

        // Show error message
        function showError(message) {
            // Create error toast
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Hide toast after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            // Create success toast
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Hide toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // Close modal when clicking outside
        document.getElementById('reservationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
