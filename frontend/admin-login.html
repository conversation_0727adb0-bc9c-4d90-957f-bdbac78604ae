<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-form {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .input-group {
            position: relative;
        }
        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label {
            transform: translateY(-1.5rem) scale(0.8);
            color: #667eea;
        }
        .input-group label {
            transition: all 0.3s ease;
            position: absolute;
            left: 1rem;
            top: 1rem;
            pointer-events: none;
        }
    </style>
</head>
<body class="login-container flex items-center justify-center p-4">
    <div class="login-form rounded-2xl shadow-2xl p-8 w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-shield-alt text-white text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Đăng Nhập Hệ Thống</h1>
            <p class="text-gray-600">Dành cho Admin và Nhân viên</p>
        </div>

        <!-- Account Types Info -->
        <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
            <h3 class="text-sm font-semibold text-gray-700 mb-2">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>Loại tài khoản
            </h3>
            <div class="space-y-2 text-xs text-gray-600">
                <div class="flex items-center">
                    <i class="fas fa-crown text-yellow-500 mr-2"></i>
                    <span><strong>Admin:</strong> Toàn quyền quản lý hệ thống</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-user-tie text-blue-500 mr-2"></i>
                    <span><strong>Nhân viên:</strong> Xem hóa đơn (không thấy số tiền)</span>
                </div>
            </div>
        </div>

        <!-- Login Form -->
        <form id="loginForm" class="space-y-6">
            <!-- Username -->
            <div class="input-group">
                <input type="text" id="username" name="username" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                       placeholder=" ">
                <label for="username" class="text-gray-500">Tên đăng nhập</label>
            </div>

            <!-- Password -->
            <div class="input-group">
                <input type="password" id="password" name="password" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                       placeholder=" ">
                <label for="password" class="text-gray-500">Mật khẩu</label>
                <button type="button" id="togglePassword" class="absolute right-3 top-3 text-gray-500 hover:text-gray-700">
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <!-- Remember Me -->
            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" id="rememberMe" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <span class="ml-2 text-sm text-gray-600">Ghi nhớ đăng nhập</span>
                </label>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="loginBtn" 
                    class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Đăng Nhập
            </button>
        </form>

        <!-- Error Message -->
        <div id="errorMessage" class="hidden mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <span id="errorText"></span>
        </div>

        <!-- Login Instructions -->
        <div class="mt-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
            <h3 class="text-sm font-semibold text-gray-700 mb-3">
                <i class="fas fa-info-circle text-gray-500 mr-2"></i>Hướng dẫn đăng nhập
            </h3>
            <div class="text-xs text-gray-600 space-y-2">
                <div class="flex items-start">
                    <i class="fas fa-crown text-yellow-500 mr-2 mt-0.5"></i>
                    <span><strong>Admin:</strong> Sử dụng tài khoản quản trị được cấp</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-user-tie text-blue-500 mr-2 mt-0.5"></i>
                    <span><strong>Nhân viên:</strong> Sử dụng tài khoản do admin tạo</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-shield-alt text-green-500 mr-2 mt-0.5"></i>
                    <span><strong>Bảo mật:</strong> Không chia sẻ thông tin đăng nhập</span>
                </div>
            </div>
        </div>



        <!-- Footer -->
        <div class="text-center mt-6 text-sm text-gray-500">
            <p>&copy; 2024 Restaurant Management System</p>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">Đang đăng nhập...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/admin-auth.js"></script>
    <script>
        let adminAuth;

        document.addEventListener('DOMContentLoaded', function() {
            adminAuth = new AdminAuth();

            // Check if already logged in
            if (adminAuth.isLoggedIn()) {
                window.location.href = 'DanhSachHoaDon.html';
                return;
            }

            setupEventListeners();
        });

        function setupEventListeners() {
            // Toggle password visibility
            document.getElementById('togglePassword').addEventListener('click', function() {
                const passwordInput = document.getElementById('password');
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });

            // Form submission
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleLogin();
            });

            // Enter key on form fields
            document.querySelectorAll('#username, #password').forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        handleLogin();
                    }
                });
            });
        }

        function handleLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // Validate input
            if (!username || !password) {
                showError('Vui lòng nhập đầy đủ tên đăng nhập và mật khẩu');
                return;
            }

            // Debug: Check available accounts
            console.log('🔍 Debug - Checking available accounts...');
            try {
                const adminAccounts = adminAuth.getAdminAccounts();
                const staffAccounts = adminAuth.getStaffAccountsFromStorage();
                console.log('👑 Admin accounts:', adminAccounts);
                console.log('👔 Staff accounts:', staffAccounts);
                console.log('🔐 Trying to login with:', username, '/', password);

                // Check if the account exists in either storage
                const allAccounts = [...adminAccounts, ...staffAccounts];
                const foundAccount = allAccounts.find(acc => acc.username === username);
                console.log('🔍 Found account:', foundAccount);

                if (foundAccount) {
                    console.log('✅ Account exists, checking password...');
                    console.log('🔐 Stored password:', foundAccount.password);
                    console.log('🔐 Input password:', password);
                    console.log('🔐 Password match:', foundAccount.password === password);
                    console.log('🔐 Account active:', foundAccount.isActive);
                } else {
                    console.log('❌ Account not found in any storage');
                }
            } catch (error) {
                console.error('❌ Error during debug:', error);
            }

            // Show loading
            showLoading(true);
            hideError();

            // Simulate network delay
            setTimeout(() => {
                const result = adminAuth.login(username, password);
                
                showLoading(false);

                if (result.success) {
                    // Save remember me preference
                    if (rememberMe) {
                        localStorage.setItem('rememberAdmin', 'true');
                    }

                    // Show success and redirect
                    showSuccess('Đăng nhập thành công!');
                    setTimeout(() => {
                        window.location.href = 'DanhSachHoaDon.html';
                    }, 1000);
                } else {
                    showError(result.message);
                }
            }, 1000);
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorDiv.classList.remove('hidden');
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                hideError();
            }, 5000);
        }

        function hideError() {
            document.getElementById('errorMessage').classList.add('hidden');
        }

        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorDiv.className = 'mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg';
            errorText.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            errorDiv.classList.remove('hidden');
        }

        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            const loginBtn = document.getElementById('loginBtn');

            if (show) {
                overlay.classList.remove('hidden');
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang đăng nhập...';
            } else {
                overlay.classList.add('hidden');
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>Đăng Nhập';
            }
        }


    </script>
</body>
</html>
