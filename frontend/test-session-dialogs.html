<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#e53e3e">
    <title>Test Session Dialogs - Nhà Hàng Ẩm Thực Phương Nam</title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <link rel="apple-touch-icon" href="img/logoPN.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            margin: 0.25rem;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .btn-primary { background: #3b82f6; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        
        .status-box {
            padding: 1rem;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            margin: 0.5rem 0;
        }
        
        .status-success { border-color: #10b981; background: #f0fdf4; }
        .status-warning { border-color: #f59e0b; background: #fffbeb; }
        .status-error { border-color: #ef4444; background: #fef2f2; }
        
        .logs {
            background: #1f2937;
            color: #10b981;
            padding: 1rem;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }
    </style>
</head>
<body class="p-4">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center text-white mb-6">
            <h1 class="text-3xl font-bold mb-2">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Test Session Dialogs
            </h1>
            <p class="text-lg opacity-90">Kiểm tra các hộp thoại thông báo phiên làm việc</p>
        </div>

        <!-- Login Section -->
        <div class="card">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-sign-in-alt mr-2 text-blue-500"></i>
                Đăng Nhập Test
            </h2>
            
            <div id="loginForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email:</label>
                        <input type="email" id="email" value="<EMAIL>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password:</label>
                        <input type="password" id="password" value="123456" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <button onclick="testLogin()" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    Đăng Nhập Test
                </button>
            </div>

            <div id="loginStatus" class="mt-4"></div>
        </div>

        <!-- Status Section -->
        <div class="card">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-info-circle mr-2 text-green-500"></i>
                Trạng Thái Hệ Thống
            </h2>
            
            <div id="authStatus" class="status-box">
                <strong>Trạng thái đăng nhập:</strong> <span id="authStatusText">Chưa đăng nhập</span>
            </div>
            
            <div id="tokenStatus" class="status-box">
                <strong>Token:</strong> <span id="tokenStatusText">Không có</span>
            </div>
        </div>

        <!-- Dialog Test Section -->
        <div class="card">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-comment-dots mr-2 text-purple-500"></i>
                Test Hộp Thoại
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <button onclick="showWarningDialog()" class="btn btn-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Warning Dialog (30s)
                </button>
                <button onclick="showExpiredDialog()" class="btn btn-danger">
                    <i class="fas fa-times-circle"></i>
                    Session Expired Dialog
                </button>
                <button onclick="showAutoLogoutNotification()" class="btn btn-secondary">
                    <i class="fas fa-bell"></i>
                    Auto Logout Notification
                </button>
                <button onclick="testInactivityLogout()" class="btn btn-primary">
                    <i class="fas fa-clock"></i>
                    Test Inactivity (10s)
                </button>
                <button onclick="forceLogout()" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    Force Logout
                </button>
                <button onclick="clearAllData()" class="btn btn-secondary">
                    <i class="fas fa-trash"></i>
                    Clear All Data
                </button>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-book mr-2 text-indigo-500"></i>
                Hướng Dẫn Test
            </h2>
            
            <div class="space-y-3 text-sm">
                <div class="p-3 bg-blue-50 rounded-lg">
                    <strong class="text-blue-700">1. Warning Dialog:</strong>
                    <p class="text-blue-600">Hiển thị cảnh báo phiên sắp hết hạn với countdown 30 giây. Có nút "Gia hạn" và "Đăng xuất".</p>
                </div>
                <div class="p-3 bg-red-50 rounded-lg">
                    <strong class="text-red-700">2. Session Expired Dialog:</strong>
                    <p class="text-red-600">Hiển thị thông báo phiên đã hết hạn, yêu cầu đăng nhập lại. Có nút "Đóng".</p>
                </div>
                <div class="p-3 bg-gray-50 rounded-lg">
                    <strong class="text-gray-700">3. Auto Logout Notification:</strong>
                    <p class="text-gray-600">Hiển thị thông báo toast ở góc phải màn hình khi tự động đăng xuất.</p>
                </div>
                <div class="p-3 bg-green-50 rounded-lg">
                    <strong class="text-green-700">4. Test Inactivity:</strong>
                    <p class="text-green-600">Mô phỏng auto-logout sau 10 giây không hoạt động (thay vì 2 phút).</p>
                </div>
            </div>
        </div>

        <!-- Logs Section -->
        <div class="card">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-terminal mr-2 text-green-500"></i>
                Logs
            </h2>
            
            <div id="logs" class="logs">
                <div>🚀 Session Dialogs Test initialized...</div>
            </div>
            
            <div class="mt-2">
                <button onclick="clearLogs()" class="btn btn-secondary">
                    <i class="fas fa-eraser"></i>
                    Clear Logs
                </button>
            </div>
        </div>
    </div>

    <!-- Include Auth System -->
    <script src="js/auth.js"></script>

    <script>
        let statusInterval;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Session Dialogs Test page loaded');
            updateStatus();
            startStatusMonitoring();
        });

        // Log function
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        // Test login
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            log(`🔐 Attempting login with ${email}...`);
            
            try {
                const result = await auth.login(email, password);
                log('✅ Login successful!');
                log(`🔑 Token expires at: ${new Date(result.tokenExpiry)}`);
                updateStatus();
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
                updateStatus();
            }
        }

        // Update status display
        function updateStatus() {
            const authStatusEl = document.getElementById('authStatus');
            const authStatusText = document.getElementById('authStatusText');
            const tokenStatusText = document.getElementById('tokenStatusText');

            if (auth.isAuthenticated) {
                authStatusEl.className = 'status-box status-success';
                authStatusText.textContent = `Đã đăng nhập (${auth.user?.email || 'Unknown'})`;
                tokenStatusText.textContent = 'Có token hợp lệ';
            } else {
                authStatusEl.className = 'status-box status-error';
                authStatusText.textContent = 'Chưa đăng nhập';
                tokenStatusText.textContent = 'Không có';
            }
        }

        // Start status monitoring
        function startStatusMonitoring() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }

            statusInterval = setInterval(() => {
                updateStatus();
            }, 1000);
        }

        // Show warning dialog
        function showWarningDialog() {
            log('⚠️ Showing session warning dialog...');
            auth.showSessionWarning();
        }

        // Show expired dialog
        function showExpiredDialog() {
            log('🚨 Showing session expired dialog...');
            auth.showSessionExpiredDialog();
        }

        // Show auto logout notification
        function showAutoLogoutNotification() {
            log('🔔 Showing auto logout notification...');
            auth.showAutoLogoutNotification();
        }

        // Test inactivity logout (10 seconds instead of 2 minutes)
        function testInactivityLogout() {
            if (!auth.isAuthenticated) {
                log('❌ Please login first to test inactivity logout');
                return;
            }

            log('⏰ Starting inactivity test (10 seconds)...');
            log('🚫 Do NOT move mouse or touch screen for 10 seconds');
            
            // Temporarily override inactivity timeout
            const originalTimeout = auth.INACTIVITY_TIMEOUT;
            auth.INACTIVITY_TIMEOUT = 10000; // 10 seconds
            
            // Restart activity tracking with new timeout
            auth.stopActivityTracking();
            auth.startActivityTracking();
            
            // Restore original timeout after test
            setTimeout(() => {
                auth.INACTIVITY_TIMEOUT = originalTimeout;
                log('🔄 Inactivity timeout restored to 2 minutes');
            }, 15000);
        }

        // Force logout
        function forceLogout() {
            if (auth.isAuthenticated) {
                auth.logout();
                log('🚪 Forced logout');
                updateStatus();
            } else {
                log('⚠️ Already logged out');
            }
        }

        // Clear all data
        function clearAllData() {
            auth.clearAuthData();
            log('🗑️ All auth data cleared');
            updateStatus();
        }

        // Clear logs
        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div>🚀 Logs cleared...</div>';
        }

        // Listen for auth changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'user' || e.key === 'token') {
                log('📡 Auth state changed (storage event)');
                updateStatus();
            }
        });
    </script>
</body>
</html>
