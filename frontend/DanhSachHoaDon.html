<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Ẩm <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/admin-auth.js"></script>
    <style>
        .invoice-card {
            transition: all 0.3s ease;
            border-left: 4px solid #e5e7eb;
        }

        .invoice-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-left-color: #3b82f6;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-confirmed {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-cancelled {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-pending-approval {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }

        .filter-btn {
            transition: all 0.2s ease;
        }

        .filter-btn.active {
            background-color: #3b82f6;
            color: white;
        }

        .empty-state {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="goBack()" class="text-gray-600 hover:text-gray-800 transition-colors">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">Danh Sách Hóa Đơn</h1>
                        <p class="text-gray-600">Quản lý và xem lại các đơn hàng của bạn</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- User Info -->
                    <div class="flex items-center space-x-3 bg-gray-50 px-4 py-2 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <div id="userRoleBadge" class="px-2 py-1 rounded text-xs font-semibold">
                                <!-- Role badge will be inserted here -->
                            </div>
                            <span id="currentUserName" class="text-sm font-medium text-gray-700">
                                <!-- Username will be inserted here -->
                            </span>
                        </div>
                        <button onclick="debugAccounts()" class="text-yellow-600 hover:text-yellow-800 transition-colors mr-2" title="Debug Accounts">
                            <i class="fas fa-bug"></i>
                        </button>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 transition-colors" title="Đăng xuất">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>

                    <!-- Action Buttons (Admin Only) -->
                    <div id="adminActions" class="flex items-center space-x-3">
                        <button onclick="showRevenueStats()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-chart-line mr-2"></i>Thống kê doanh thu
                        </button>
                        <button onclick="showStaffManagement()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-users mr-2"></i>Quản lý nhân viên
                        </button>
                        <button onclick="goToReservations()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                            <i class="fas fa-calendar-check mr-2"></i>Đặt bàn
                        </button>
                    </div>

                    <!-- Common Actions -->
                    <div class="flex items-center space-x-3">
                        <button onclick="refreshList()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>Làm mới
                        </button>
                        <button onclick="goToMenu()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Đặt món mới
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Filters -->
    <div class="container mx-auto px-4 py-6">
        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterInvoices('all')" class="filter-btn active px-4 py-2 rounded-lg border border-gray-300 hover:bg-yellow-100" data-filter="all">
                        <i class="fas fa-list mr-2"></i>Tất cả
                    </button>
                    <button onclick="filterInvoices('pending-approval')" class="filter-btn px-4 py-2 rounded-lg border border-gray-300 hover:bg-yellow-100" data-filter="pending-approval">
                        <i class="fas fa-hourglass-half mr-2"></i>Chờ duyệt
                    </button>
                    <button onclick="filterInvoices('pending')" class="filter-btn px-4 py-2 rounded-lg border border-gray-300 hover:bg-yellow-100" data-filter="pending">
                        <i class="fas fa-clock mr-2"></i>Chờ xác nhận
                    </button>
                    <button onclick="filterInvoices('confirmed')" class="filter-btn px-4 py-2 rounded-lg border border-gray-300 hover:bg-yellow-100" data-filter="confirmed">
                        <i class="fas fa-check-circle mr-2"></i>Đã xác nhận
                    </button>
                    <button onclick="filterInvoices('completed')" class="filter-btn px-4 py-2 rounded-lg border border-gray-300 hover:bg-yellow-100" data-filter="completed">
                        <i class="fas fa-check-double mr-2"></i>Hoàn thành
                    </button>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Tìm kiếm hóa đơn..."
                               class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <select id="sortSelect" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="newest">Mới nhất</option>
                        <option value="oldest">Cũ nhất</option>
                        <option value="amount-high">Giá cao nhất</option>
                        <option value="amount-low">Giá thấp nhất</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Tổng hóa đơn</p>
                        <p id="totalInvoices" class="text-2xl font-bold text-gray-800">0</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-receipt text-blue-600"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Chờ duyệt</p>
                        <p id="pendingApprovalInvoices" class="text-2xl font-bold text-yellow-600">0</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-hourglass-half text-yellow-600"></i>
                    </div>
                </div>
            </div>
            <div id="totalAmountCard" class="bg-white rounded-lg shadow-sm p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Tổng tiền</p>
                        <p id="totalAmount" class="text-2xl font-bold text-green-600">0 ₫</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-dollar-sign text-green-600"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Đã hoàn thành</p>
                        <p id="completedInvoices" class="text-2xl font-bold text-purple-600">0</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-check-double text-purple-600"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Hóa đơn hôm nay</p>
                        <p id="todayInvoices" class="text-2xl font-bold text-orange-600">0</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-calendar-day text-orange-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice List -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-list mr-2 text-blue-600"></i>Danh sách hóa đơn
                </h2>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="p-8 text-center">
                <i class="fas fa-spinner fa-spin text-3xl text-blue-600 mb-4"></i>
                <p class="text-gray-600">Đang tải danh sách hóa đơn...</p>
            </div>

            <!-- Invoice Items Container -->
            <div id="invoiceList" class="hidden">
                <!-- Invoice items will be populated by JavaScript -->
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="hidden p-12 text-center">
                <div class="empty-state rounded-lg p-8 text-white mb-6">
                    <i class="fas fa-receipt text-6xl mb-4 opacity-80"></i>
                    <h3 class="text-xl font-semibold mb-2">Chưa có hóa đơn nào</h3>
                    <p class="opacity-90">Bạn chưa có đơn hàng nào. Hãy đặt món ngay để tạo hóa đơn đầu tiên!</p>
                </div>
                <button onclick="goToMenu()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-utensils mr-2"></i>Đặt món ngay
                </button>
            </div>

            <!-- No Results State -->
            <div id="noResultsState" class="hidden p-8 text-center">
                <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-600 mb-2">Không tìm thấy kết quả</h3>
                <p class="text-gray-500">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
            </div>
        </div>

        <!-- Pagination -->
        <div id="pagination" class="hidden mt-6 flex justify-center">
            <div class="flex items-center space-x-2">
                <button onclick="changePage('prev')" class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="pageNumbers" class="flex space-x-1">
                    <!-- Page numbers will be populated by JavaScript -->
                </div>
                <button onclick="changePage('next')" class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Transfer Proof Modal -->
    <div id="transferProofModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-2xl mx-4 max-h-[90vh] overflow-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-image mr-2 text-blue-600"></i>
                    Hình Ảnh Chuyển Khoản
                </h3>
                <button onclick="closeTransferProofModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="transferProofContent" class="text-center">
                <!-- Image will be loaded here -->
            </div>
            <div class="mt-4 flex justify-end space-x-3">
                <button onclick="closeTransferProofModal()"
                        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                    <i class="fas fa-times mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Invoice Modal -->
    <div id="editInvoiceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-4xl mx-4 max-h-[90vh] overflow-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-edit mr-2 text-yellow-600"></i>
                    Chỉnh Sửa Hóa Đơn
                </h3>
                <button onclick="closeEditModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="editInvoiceForm" class="space-y-6">
                <!-- Invoice Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mã hóa đơn</label>
                        <input type="text" id="editInvoiceNumber" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                        <select id="editStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                            <option value="pending">Chờ xác nhận</option>
                            <option value="pending-approval">Chờ duyệt</option>
                            <option value="confirmed">Đã xác nhận</option>
                            <option value="completed">Hoàn thành</option>
                            <option value="cancelled">Đã hủy</option>
                        </select>
                    </div>
                </div>

                <!-- Customer Info -->
                <div class="border-t pt-4">
                    <h4 class="text-lg font-medium text-gray-800 mb-3">Thông tin khách hàng</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tên khách hàng</label>
                            <input type="text" id="editCustomerName"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="editCustomerEmail"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Số điện thoại</label>
                            <input type="tel" id="editCustomerPhone"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                        </div>
                    </div>
                </div>

                <!-- Order Details -->
                <div class="border-t pt-4">
                    <h4 class="text-lg font-medium text-gray-800 mb-3">Chi tiết đơn hàng</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Loại đơn</label>
                            <select id="editOrderType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                                <option value="dine-in">Tại chỗ</option>
                                <option value="delivery">Giao hàng</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phương thức thanh toán</label>
                            <select id="editPaymentMethod" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                                <option value="cash">Tiền mặt</option>
                                <option value="bank_transfer">Chuyển khoản</option>
                                <option value="card">Thẻ tín dụng</option>
                                <option value="momo">MoMo</option>
                                <option value="vnpay">VNPay</option>
                            </select>
                        </div>
                    </div>

                    <div id="editDeliveryAddressSection" class="hidden mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Địa chỉ giao hàng</label>
                        <textarea id="editDeliveryAddress" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ghi chú</label>
                        <textarea id="editNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500"></textarea>
                    </div>
                </div>

                <!-- Items List -->
                <div class="border-t pt-4">
                    <h4 class="text-lg font-medium text-gray-800 mb-3">Danh sách món ăn</h4>
                    <div id="editItemsList" class="space-y-2">
                        <!-- Items will be populated here -->
                    </div>
                    <div class="mt-4 pt-4 border-t">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Tổng cộng:</span>
                            <span id="editTotalAmount" class="text-red-600">0đ</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t">
                    <button type="button" onclick="closeEditModal()"
                            class="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="fas fa-times mr-2"></i>Hủy
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Staff Management Modal -->
    <div id="staffManagementModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-4xl mx-4 max-h-[90vh] overflow-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-users mr-2 text-purple-600"></i>
                    Quản Lý Nhân Viên
                </h3>
                <button onclick="closeStaffModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Create Staff Form -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-lg font-medium mb-4">Tạo Tài Khoản Nhân Viên Mới</h4>
                <form id="createStaffForm" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tên đăng nhập</label>
                        <input type="text" id="staffUsername" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mật khẩu</label>
                        <input type="password" id="staffPassword" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Họ tên</label>
                        <input type="text" id="staffFullName" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500">
                    </div>
                    <div class="md:col-span-3">
                        <button type="submit" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Tạo Tài Khoản
                        </button>
                    </div>
                </form>
            </div>

            <!-- Staff List -->
            <div>
                <h4 class="text-lg font-medium mb-4">Danh Sách Nhân Viên</h4>
                <div id="staffList" class="space-y-3">
                    <!-- Staff list will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Statistics Modal -->
    <div id="revenueStatsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">
                    <i class="fas fa-chart-line mr-2 text-blue-600"></i>Thống Kê Doanh Thu
                </h2>
                <button onclick="closeRevenueStats()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Date Range Selector -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Từ ngày:</label>
                        <input type="date" id="startDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Đến ngày:</label>
                        <input type="date" id="endDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button onclick="updateRevenueStats()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fas fa-search mr-2"></i>Thống kê
                        </button>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-blue-600 font-medium">Tổng đơn hàng</p>
                            <p id="totalOrders" class="text-2xl font-bold text-blue-800">0</p>
                        </div>
                        <div class="bg-blue-100 p-3 rounded-full">
                            <i class="fas fa-receipt text-blue-600"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-green-600 font-medium">Tổng doanh thu</p>
                            <p id="totalRevenue" class="text-2xl font-bold text-green-800">0 ₫</p>
                        </div>
                        <div class="bg-green-100 p-3 rounded-full">
                            <i class="fas fa-dollar-sign text-green-600"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-yellow-600 font-medium">Đơn trung bình</p>
                            <p id="averageOrder" class="text-2xl font-bold text-yellow-800">0 ₫</p>
                        </div>
                        <div class="bg-yellow-100 p-3 rounded-full">
                            <i class="fas fa-calculator text-yellow-600"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-purple-600 font-medium">Đơn hôm nay</p>
                            <p id="todayOrders" class="text-2xl font-bold text-purple-800">0</p>
                        </div>
                        <div class="bg-purple-100 p-3 rounded-full">
                            <i class="fas fa-calendar-day text-purple-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Revenue Chart -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Doanh thu theo ngày</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div id="dailyRevenueChart" class="h-64 flex items-center justify-center text-gray-500">
                        <div class="text-center">
                            <i class="fas fa-chart-bar text-4xl mb-2"></i>
                            <p>Chọn khoảng thời gian để xem biểu đồ</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Method Breakdown -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Phương thức thanh toán</h3>
                    <div id="paymentMethodStats" class="space-y-3">
                        <!-- Payment method stats will be populated here -->
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Top món ăn bán chạy</h3>
                    <div id="topDishesStats" class="space-y-3">
                        <!-- Top dishes stats will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Admin Auth
        let adminAuth;

        // Check authentication and redirect if needed
        document.addEventListener('DOMContentLoaded', function() {
            adminAuth = new AdminAuth();

            // Check if user is logged in and has permission
            if (!adminAuth.checkPageAccess('view_invoices')) {
                return; // Will redirect to login page
            }

            // Initialize page
            initializePage();
        });

        // Initialize page with authentication
        function initializePage() {
            // Update UI based on user role
            updateUIForUserRole();

            // Load invoices
            loadInvoices();

            // Setup staff management if admin
            if (adminAuth.isAdmin()) {
                setupStaffManagement();
            }
        }

        // Update UI based on user role
        function updateUIForUserRole() {
            const currentUser = adminAuth.getCurrentUser();
            if (!currentUser) return;

            // Update user info in header
            document.getElementById('currentUserName').textContent = currentUser.fullName;

            const roleBadge = document.getElementById('userRoleBadge');
            if (currentUser.role === 'admin') {
                roleBadge.textContent = 'ADMIN';
                roleBadge.className = 'px-2 py-1 rounded text-xs font-semibold bg-red-100 text-red-800';
            } else {
                roleBadge.textContent = 'NHÂN VIÊN';
                roleBadge.className = 'px-2 py-1 rounded text-xs font-semibold bg-blue-100 text-blue-800';
            }

            // Show/hide admin actions
            const adminActions = document.getElementById('adminActions');
            if (adminAuth.isAdmin()) {
                adminActions.classList.remove('hidden');
            } else {
                adminActions.classList.add('hidden');
            }

            // Show/hide total amount for staff
            const totalAmountCard = document.getElementById('totalAmountCard');
            if (adminAuth.isAdmin()) {
                totalAmountCard.classList.remove('hidden');
            } else {
                totalAmountCard.classList.add('hidden');
            }
        }

        // Debug accounts function
        function debugAccounts() {
            if (adminAuth) {
                const result = adminAuth.debugAllAccounts();

                // Test login with each staff account
                console.log('🧪 Testing login for all staff accounts...');
                result.staffAccounts.forEach(staff => {
                    console.log(`🧪 Testing login: ${staff.username} / ${staff.password}`);
                    const loginResult = adminAuth.login(staff.username, staff.password);
                    console.log(`🧪 Login result for ${staff.username}:`, loginResult);
                });

                alert(`Debug Info:\n\nAdmin accounts: ${result.adminAccounts.length}\nStaff accounts: ${result.staffAccounts.length}\n\nCheck console for details and login tests.`);
            } else {
                alert('AdminAuth not initialized!');
            }
        }

        // Logout function
        function logout() {
            if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
                adminAuth.logout();
            }
        }

        // Show staff management modal (admin only)
        function showStaffManagement() {
            if (!adminAuth.hasPermission('create_staff')) {
                alert('Bạn không có quyền quản lý nhân viên!');
                return;
            }

            document.getElementById('staffManagementModal').classList.remove('hidden');
            loadStaffList();
        }

        // Close staff management modal
        function closeStaffModal() {
            document.getElementById('staffManagementModal').classList.add('hidden');
        }

        // Setup staff management
        function setupStaffManagement() {
            // Create staff form submission
            document.getElementById('createStaffForm').addEventListener('submit', function(e) {
                e.preventDefault();
                createStaff();
            });
        }

        // Create new staff
        function createStaff() {
            const username = document.getElementById('staffUsername').value.trim();
            const password = document.getElementById('staffPassword').value;
            const fullName = document.getElementById('staffFullName').value.trim();

            if (!username || !password || !fullName) {
                alert('Vui lòng điền đầy đủ thông tin!');
                return;
            }

            console.log('🔧 Creating staff account:', { username, password, fullName });

            const result = adminAuth.createStaffAccount({
                username: username,
                password: password,
                fullName: fullName
            });

            console.log('🔧 Create staff result:', result);

            if (result.success) {
                alert(`✅ Tạo tài khoản nhân viên thành công!\n\nUsername: ${username}\nPassword: ${password}\n\nBạn có thể đăng nhập bằng tài khoản này.`);
                document.getElementById('createStaffForm').reset();
                loadStaffList();

                // Debug: Check if account was saved
                setTimeout(() => {
                    const staffAccounts = adminAuth.getStaffAccountsFromStorage();
                    console.log('🔧 Staff accounts after creation:', staffAccounts);
                }, 100);
            } else {
                alert('❌ ' + result.message);
            }
        }

        // Load staff list
        function loadStaffList() {
            const staffAccounts = adminAuth.getStaffAccounts();
            const staffList = document.getElementById('staffList');

            if (staffAccounts.length === 0) {
                staffList.innerHTML = '<p class="text-gray-500 text-center py-4">Chưa có nhân viên nào</p>';
                return;
            }

            staffList.innerHTML = staffAccounts.map(staff => `
                <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">${staff.fullName}</div>
                            <div class="text-sm text-gray-500">@${staff.username}</div>
                            <div class="text-xs text-gray-400">Tạo: ${new Date(staff.createdAt).toLocaleDateString('vi-VN')}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            ${staff.isActive ? 'Hoạt động' : 'Tạm khóa'}
                        </span>
                        <button onclick="deleteStaff('${staff.id}')"
                                class="text-red-600 hover:text-red-800 p-1" title="Xóa nhân viên">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Delete staff
        function deleteStaff(staffId) {
            if (!confirm('Bạn có chắc chắn muốn xóa tài khoản nhân viên này?')) {
                return;
            }

            const result = adminAuth.deleteStaffAccount(staffId);
            if (result.success) {
                alert('✅ Đã xóa tài khoản nhân viên!');
                loadStaffList();
            } else {
                alert('❌ ' + result.message);
            }
        }

        // Debug admin status
        function debugAdminStatus() {
            const debugInfo = {
                adminAuth: !!adminAuth,
                isLoggedIn: adminAuth ? adminAuth.isLoggedIn() : false,
                isAdmin: adminAuth ? adminAuth.isAdmin() : false,
                isStaff: adminAuth ? adminAuth.isStaff() : false,
                currentUser: adminAuth ? adminAuth.getCurrentUser() : null,
                hasApprovePermission: adminAuth ? adminAuth.hasPermission('approve_invoices') : false,
                pendingInvoices: allInvoices.filter(inv => inv.status === 'pending-approval').length
            };

            console.log('🔍 Admin Debug Info:', debugInfo);
            alert('Debug Info (xem console):\n' + JSON.stringify(debugInfo, null, 2));
        }

        // Global variables
        let allInvoices = [];
        let filteredInvoices = [];
        let currentFilter = 'all';
        let currentPage = 1;
        const itemsPerPage = 10;

        // Utility functions
        function formatPrice(price) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(price);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('vi-VN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatDateShort(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        }

        function generateInvoiceNumber(timestamp) {
            const date = new Date(timestamp);
            const random = Math.floor(Math.random() * 1000);
            return `HD${date.getTime().toString().slice(-6)}${random.toString().padStart(3, '0')}`;
        }

        // Navigation functions
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                goToHome();
            }
        }

        function goToMenu() {
            window.location.href = 'Menu-new.html';
        }

        function goToReservations() {
            window.location.href = 'DanhSachDatBan.html';
        }

        function goToHome() {
            window.location.href = 'Index-new.html';
        }

        function viewInvoiceDetail(invoiceId) {
            // Set the invoice data in localStorage and redirect to detail page
            const invoice = allInvoices.find(inv => inv.id === invoiceId);
            if (invoice) {
                localStorage.setItem('currentOrder', JSON.stringify(invoice));
                window.location.href = 'HoaDon.html';
            }
        }

        // Create sample pending approval invoices for testing
        function createSamplePendingInvoices() {
            const sampleInvoices = [
                {
                    id: 'pending_001',
                    invoiceNumber: 'HD001001',
                    customerName: 'Nguyễn Văn A',
                    customerEmail: '<EMAIL>',
                    customerPhone: '**********',
                    items: [
                        { name: 'Phở Bò Tái', quantity: 2, price: 65000, total: 130000 },
                        { name: 'Chả Cá Lã Vọng', quantity: 1, price: 120000, total: 120000 }
                    ],
                    total: 250000,
                    orderType: 'dine-in',
                    status: 'pending-approval',
                    payment_method: 'bank_transfer',
                    payment_status: 'pending',
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                    address: null,
                    notes: 'Khách hàng thanh toán qua chuyển khoản ngân hàng',
                    transfer_proof: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmOWZmIiBzdHJva2U9IiNjYWY0ZmYiIHN0cm9rZS13aWR0aD0iMiIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iMzAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IiMzNzQ5NWUiPkjDrG5oIMOhbmggQ2h1eeG7g24gS2hvYW4gTcOidTwvdGV4dD4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjM2NjZiIj5T4buRIHRp4buBbjogMjUwLDAwMCBWTsSTPC90ZXh0PgogIDx0ZXh0IHg9IjUwJSIgeT0iNzAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5Y2EzYWYiPk5nw6J5OiAyMy8xMi8yMDI0IC0gMTQ6MzA8L3RleHQ+CiAgPGNpcmNsZSBjeD0iMzUwIiBjeT0iNTAiIHI9IjIwIiBmaWxsPSIjMTBiOTgxIi8+CiAgPHRleHQgeD0iMzUwIiB5PSI1NSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSI+4pyTPC90ZXh0Pgo8L3N2Zz4='
                },
                {
                    id: 'pending_002',
                    invoiceNumber: 'HD001002',
                    customerName: 'Trần Thị B',
                    customerEmail: '<EMAIL>',
                    customerPhone: '**********',
                    items: [
                        { name: 'Bún Bò Huế', quantity: 1, price: 55000, total: 55000 },
                        { name: 'Nem Nướng Nha Trang', quantity: 2, price: 45000, total: 90000 }
                    ],
                    total: 145000,
                    orderType: 'delivery',
                    status: 'pending-approval',
                    payment_method: 'bank_transfer',
                    payment_status: 'pending',
                    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
                    address: '123 Nguyễn Huệ, Quận 1, TP.HCM',
                    notes: 'Giao hàng tận nơi, thanh toán chuyển khoản',
                    transfer_proof: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVmM2M3IiBzdHJva2U9IiNmYmNmOWEiIHN0cm9rZS13aWR0aD0iMiIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iMzAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IiM5MjQwMGUiPkjDrG5oIMOhbmggQ2h1eeG7g24gS2hvYW4gTcOidTwvdGV4dD4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTI0MDBmIj5T4buRIHRp4buBbjogMzIwLDAwMCBWTsSTPC90ZXh0PgogIDx0ZXh0IHg9IjUwJSIgeT0iNzAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiNhMTYyMDciPk5nw6J5OiAyMy8xMi8yMDI0IC0gMTU6NDU8L3RleHQ+CiAgPGNpcmNsZSBjeD0iMzUwIiBjeT0iNTAiIHI9IjIwIiBmaWxsPSIjZWFiMzA4Ii8+CiAgPHRleHQgeD0iMzUwIiB5PSI1NSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSI+4pyTPC90ZXh0Pgo8L3N2Zz4='
                }
            ];

            return sampleInvoices;
        }

        // Load invoices from localStorage
        function loadInvoices() {
            try {
                // Get all invoices from localStorage
                const storedInvoices = localStorage.getItem('invoiceHistory');
                if (storedInvoices) {
                    allInvoices = JSON.parse(storedInvoices);
                } else {
                    allInvoices = [];
                }

                // Sample invoices will only be created manually via "Tạo HĐ chờ duyệt" button

                // Add invoices from payment flow
                const paymentInvoices = localStorage.getItem('invoiceList');
                if (paymentInvoices) {
                    const invoicesFromPayment = JSON.parse(paymentInvoices);
                    console.log('✅ Found invoices from payment:', invoicesFromPayment.length);

                    // Convert payment invoices to display format
                    invoicesFromPayment.forEach(paymentInvoice => {
                        const invoice = paymentInvoice.invoice || paymentInvoice;
                        const details = paymentInvoice.details || [];

                        const displayInvoice = {
                            id: invoice.id_hoa_don || invoice.id || generateInvoiceId(),
                            invoiceNumber: `HD${String(invoice.id_hoa_don || invoice.id || Math.floor(Math.random() * 1000)).padStart(6, '0')}`,
                            customerName: invoice.customer_info?.full_name || 'Nguyễn Huỳnh Kỳ Nhật Nhật',
                            customerEmail: invoice.customer_info?.email || '<EMAIL>',
                            customerPhone: invoice.customer_info?.phone || '0123456789',
                            items: details.map(detail => ({
                                name: detail.ten_mon || detail.name || 'Món ăn',
                                quantity: detail.so_luong || detail.quantity || 1,
                                price: detail.don_gia || detail.price || 0,
                                total: detail.thanh_tien || (detail.don_gia * detail.so_luong) || 0
                            })),
                            total: invoice.tong_tien || invoice.total || 0,
                            orderType: invoice.loai_don === 'giao_hang' ? 'delivery' : 'dine-in',
                            status: mapInvoiceStatus(invoice.trang_thai),
                            payment_method: invoice.payment_method || 'cash',
                            payment_status: invoice.payment_status || 'completed',
                            createdAt: invoice.ngay_tao || paymentInvoice.timestamp || new Date().toISOString(),
                            address: invoice.dia_chi_giao_hang || null,
                            notes: invoice.ghi_chu || null,
                            transfer_proof: invoice.transfer_proof || null // Lưu hình ảnh chuyển khoản
                        };

                        // Check if not already exists
                        const existingIndex = allInvoices.findIndex(inv => inv.id === displayInvoice.id);
                        if (existingIndex === -1) {
                            allInvoices.unshift(displayInvoice);
                        }
                    });

                    saveInvoiceHistory();
                }

                // Add current order if exists and not already in history
                const currentOrder = localStorage.getItem('currentOrder');
                if (currentOrder) {
                    const order = JSON.parse(currentOrder);
                    const orderId = order.id || generateInvoiceId();

                    // Check if this order is already in history
                    const existingIndex = allInvoices.findIndex(inv => inv.id === orderId);
                    if (existingIndex === -1) {
                        // Add to history
                        const invoice = {
                            ...order,
                            id: orderId,
                            invoiceNumber: generateInvoiceNumber(order.timestamp || Date.now()),
                            status: 'confirmed',
                            createdAt: order.timestamp || new Date().toISOString()
                        };
                        allInvoices.unshift(invoice);
                        saveInvoiceHistory();
                    }
                }

                filteredInvoices = [...allInvoices];
                updateStatistics();
                renderInvoices();

                // Hide loading state
                document.getElementById('loadingState').style.display = 'none';

                if (allInvoices.length === 0) {
                    showEmptyState();
                } else {
                    showInvoiceList();
                }

            } catch (error) {
                console.error('Error loading invoices:', error);
                showEmptyState();
            }
        }

        function generateInvoiceId() {
            return 'inv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function saveInvoiceHistory() {
            localStorage.setItem('invoiceHistory', JSON.stringify(allInvoices));
        }

        // Update statistics
        function updateStatistics() {
            const total = allInvoices.length;
            const totalAmount = allInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
            const completed = allInvoices.filter(inv => inv.status === 'completed').length;
            const pendingApproval = allInvoices.filter(inv => inv.status === 'pending-approval').length;

            const today = new Date().toDateString();
            const todayCount = allInvoices.filter(inv => {
                const invDate = new Date(inv.createdAt || inv.timestamp).toDateString();
                return invDate === today;
            }).length;

            document.getElementById('totalInvoices').textContent = total;
            document.getElementById('pendingApprovalInvoices').textContent = pendingApproval;
            document.getElementById('totalAmount').textContent = formatPrice(totalAmount);
            document.getElementById('completedInvoices').textContent = completed;
            document.getElementById('todayInvoices').textContent = todayCount;
        }

        // Filter invoices
        function filterInvoices(filter) {
            currentFilter = filter;
            currentPage = 1;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            // Apply filter
            if (filter === 'all') {
                filteredInvoices = [...allInvoices];
            } else {
                filteredInvoices = allInvoices.filter(inv => inv.status === filter);
            }

            // Apply search if exists
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (searchTerm) {
                applySearch(searchTerm);
            }

            renderInvoices();
        }

        // Search functionality
        function applySearch(searchTerm) {
            if (!searchTerm) {
                filterInvoices(currentFilter);
                return;
            }

            filteredInvoices = filteredInvoices.filter(invoice => {
                const searchLower = searchTerm.toLowerCase();
                return (
                    (invoice.invoiceNumber && invoice.invoiceNumber.toLowerCase().includes(searchLower)) ||
                    (invoice.items && invoice.items.some(item =>
                        (item.ten_mon || item.name || '').toLowerCase().includes(searchLower)
                    )) ||
                    (invoice.paymentMethod && invoice.paymentMethod.toLowerCase().includes(searchLower))
                );
            });

            renderInvoices();
        }

        // Sort functionality
        function sortInvoices(sortBy) {
            switch (sortBy) {
                case 'newest':
                    filteredInvoices.sort((a, b) => new Date(b.createdAt || b.timestamp) - new Date(a.createdAt || a.timestamp));
                    break;
                case 'oldest':
                    filteredInvoices.sort((a, b) => new Date(a.createdAt || a.timestamp) - new Date(b.createdAt || b.timestamp));
                    break;
                case 'amount-high':
                    filteredInvoices.sort((a, b) => (b.total || 0) - (a.total || 0));
                    break;
                case 'amount-low':
                    filteredInvoices.sort((a, b) => (a.total || 0) - (b.total || 0));
                    break;
            }
            renderInvoices();
        }

        // Render invoices
        function renderInvoices() {
            const container = document.getElementById('invoiceList');

            if (filteredInvoices.length === 0) {
                showNoResults();
                return;
            }

            // Calculate pagination
            const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageInvoices = filteredInvoices.slice(startIndex, endIndex);

            container.innerHTML = '';

            pageInvoices.forEach(invoice => {
                const invoiceCard = createInvoiceCard(invoice);
                container.appendChild(invoiceCard);
            });

            showInvoiceList();
            renderPagination(totalPages);
        }

        // Create invoice card
        function createInvoiceCard(invoice) {
            const card = document.createElement('div');
            card.className = 'invoice-card border-b border-gray-200 p-4 hover:bg-gray-50 cursor-pointer';
            card.onclick = () => viewInvoiceDetail(invoice.id);

            const statusClass = getStatusClass(invoice.status);
            const statusText = getStatusText(invoice.status);
            const paymentMethodText = getPaymentMethodText(invoice.payment_method);

            const itemsPreview = (invoice.items || []).slice(0, 2).map(item =>
                item.ten_mon || item.name || 'Món ăn'
            ).join(', ');
            const moreItems = (invoice.items || []).length > 2 ? ` và ${(invoice.items || []).length - 2} món khác` : '';

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <h3 class="font-semibold text-gray-800">${invoice.invoiceNumber || 'HD000001'}</h3>
                                <span class="status-badge ${statusClass}">
                                    <i class="fas ${getStatusIcon(invoice.status)} mr-1"></i>${statusText}
                                </span>
                            </div>
                            <div class="text-right">
                                ${adminAuth && adminAuth.isAdmin() ? `
                                    <p class="font-bold text-lg text-blue-600">${formatPrice(invoice.total || 0)}</p>
                                ` : `
                                    <p class="font-bold text-lg text-gray-400">
                                        <i class="fas fa-lock mr-1"></i>***
                                    </p>
                                `}
                                <p class="text-sm text-gray-500">${formatDateShort(invoice.createdAt || invoice.timestamp)}</p>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <p class="text-gray-600 mb-1">
                                    <i class="fas fa-utensils mr-2 text-blue-500"></i>Món ăn:
                                </p>
                                <p class="text-gray-800">${itemsPreview}${moreItems}</p>
                            </div>
                            <div>
                                <p class="text-gray-600 mb-1">
                                    <i class="fas fa-credit-card mr-2 text-green-500"></i>Thanh toán:
                                </p>
                                <p class="text-gray-800">${paymentMethodText}</p>
                            </div>
                            <div>
                                <p class="text-gray-600 mb-1">
                                    <i class="fas fa-truck mr-2 text-purple-500"></i>Loại đơn:
                                </p>
                                <p class="text-gray-800">${invoice.orderType === 'giao_hang' ? 'Giao hàng' : 'Tại chỗ'}</p>
                            </div>
                        </div>

                        ${invoice.deliveryAddress ? `
                            <div class="mt-2 text-sm">
                                <p class="text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>Địa chỉ:
                                    <span class="text-gray-800">${invoice.deliveryAddress}</span>
                                </p>
                            </div>
                        ` : ''}
                    </div>

                    <div class="ml-4 flex flex-col items-center space-y-2">
                        <button onclick="event.stopPropagation(); viewInvoiceDetail('${invoice.id}')"
                                class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                            <i class="fas fa-eye mr-1"></i>Xem
                        </button>
                        ${adminAuth && adminAuth.hasPermission('edit_invoices') ? `
                            <button onclick="event.stopPropagation(); editInvoice('${invoice.id}')"
                                    class="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700 transition-colors">
                                <i class="fas fa-edit mr-1"></i>Sửa
                            </button>
                        ` : ''}
                        ${adminAuth && adminAuth.hasPermission('delete_invoices') ? `
                            <button onclick="event.stopPropagation(); deleteInvoice('${invoice.id}')"
                                    class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-1"></i>Xóa
                            </button>
                        ` : ''}
                        ${(() => {
                            let buttons = '';

                            // Nút duyệt cho hóa đơn chờ duyệt (chuyển khoản)
                            if (invoice.status === 'pending-approval' && adminAuth && adminAuth.hasPermission('approve_invoices')) {
                                buttons += `
                                    <button onclick="event.stopPropagation(); approveInvoice('${invoice.id}')"
                                            class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                                        <i class="fas fa-check mr-1"></i>Duyệt
                                    </button>
                                    <button onclick="event.stopPropagation(); rejectInvoice('${invoice.id}')"
                                            class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors">
                                        <i class="fas fa-times mr-1"></i>Từ chối
                                    </button>
                                `;
                            }

                            // Nút xác nhận thanh toán cho đơn hàng tiền mặt
                            if (invoice.status === 'pending' && invoice.payment_method === 'cash' && adminAuth && adminAuth.hasPermission('approve_invoices')) {
                                buttons += `
                                    <button onclick="event.stopPropagation(); confirmCashPayment('${invoice.id}')"
                                            class="bg-emerald-600 text-white px-3 py-1 rounded text-sm hover:bg-emerald-700 transition-colors">
                                        <i class="fas fa-money-bill mr-1"></i>Xác nhận thanh toán
                                    </button>
                                `;
                            }

                            return buttons;
                        })()}
                        ${invoice.transfer_proof ? `
                            <button onclick="event.stopPropagation(); viewTransferProof('${invoice.id}')"
                                    class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                                <i class="fas fa-image mr-1"></i>Xem ảnh CK
                            </button>
                        ` : ''}
                        <button onclick="event.stopPropagation(); printInvoice('${invoice.id}')"
                                class="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors">
                            <i class="fas fa-print mr-1"></i>In
                        </button>
                    </div>
                </div>
            `;

            return card;
        }

        // Map invoice status from backend to frontend
        function mapInvoiceStatus(backendStatus) {
            switch (backendStatus) {
                case 'cho_duyet': return 'pending-approval';
                case 'da_duyet': return 'approved';
                case 'cho_xac_nhan': return 'confirmed';
                case 'dang_phuc_vu': return 'confirmed';
                case 'hoan_thanh': return 'completed';
                case 'da_huy': return 'cancelled';
                default: return 'pending';
            }
        }

        // Helper functions for status
        function getStatusClass(status) {
            switch (status) {
                case 'pending': return 'status-pending';
                case 'pending-approval': return 'status-pending-approval';
                case 'approved': return 'status-approved';
                case 'confirmed': return 'status-confirmed';
                case 'completed': return 'status-completed';
                case 'cancelled': return 'status-cancelled';
                default: return 'status-pending';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'pending': return 'Chờ xác nhận';
                case 'pending-approval': return 'Chờ duyệt';
                case 'approved': return 'Đã duyệt';
                case 'confirmed': return 'Đã xác nhận';
                case 'completed': return 'Hoàn thành';
                case 'cancelled': return 'Đã hủy';
                default: return 'Chờ xác nhận';
            }
        }

        function getStatusIcon(status) {
            switch (status) {
                case 'pending': return 'fa-clock';
                case 'pending-approval': return 'fa-hourglass-half';
                case 'approved': return 'fa-check-circle';
                case 'confirmed': return 'fa-check-circle';
                case 'completed': return 'fa-check-double';
                case 'cancelled': return 'fa-times-circle';
                default: return 'fa-clock';
            }
        }

        function getPaymentMethodText(method) {
            switch (method) {
                case 'bank_transfer': return 'Chuyển khoản ngân hàng';
                case 'momo': return 'MoMo';
                case 'vnpay': return 'VNPay';
                case 'card': return 'Thẻ tín dụng';
                case 'cash': return 'Tiền mặt';
                default: return 'Tiền mặt';
            }
        }

        // Print invoice
        function printInvoice(invoiceId) {
            const invoice = allInvoices.find(inv => inv.id === invoiceId);
            if (invoice) {
                localStorage.setItem('currentOrder', JSON.stringify(invoice));
                const printWindow = window.open('HoaDon.html', '_blank');
                printWindow.onload = function() {
                    setTimeout(() => {
                        printWindow.print();
                    }, 1000);
                };
            }
        }

        // Approve invoice
        function approveInvoice(invoiceId) {
            // Check permission
            if (!adminAuth || !adminAuth.hasPermission('approve_invoices')) {
                alert('Bạn không có quyền duyệt hóa đơn!');
                return;
            }

            if (!confirm('Bạn có chắc chắn muốn duyệt hóa đơn này?')) {
                return;
            }

            try {
                console.log('🔍 Approving invoice:', invoiceId);

                // Update invoice status in local data
                updateInvoiceStatus(invoiceId, 'confirmed');

                // Show success message
                alert('✅ Hóa đơn đã được duyệt thành công!\nTrạng thái đã chuyển sang "Đã xác nhận".');

                // If currently viewing pending approval filter, refresh to remove approved invoice
                if (currentFilter === 'pending-approval') {
                    filterInvoices('pending-approval');
                }

                // Refresh the display
                updateStatistics();
                renderInvoices();

                console.log('✅ Invoice approved successfully');
            } catch (error) {
                console.error('❌ Error approving invoice:', error);
                alert('Có lỗi xảy ra khi duyệt hóa đơn. Vui lòng thử lại.');
            }
        }

        // Reject invoice
        function rejectInvoice(invoiceId) {
            // Check permission
            if (!adminAuth || !adminAuth.hasPermission('reject_invoices')) {
                alert('Bạn không có quyền từ chối hóa đơn!');
                return;
            }

            if (!confirm('Bạn có chắc chắn muốn từ chối hóa đơn này?')) {
                return;
            }

            try {
                console.log('🔍 Rejecting invoice:', invoiceId);

                // Update invoice status in local data
                updateInvoiceStatus(invoiceId, 'cancelled');

                // Show success message
                alert('❌ Hóa đơn đã bị từ chối!\nTrạng thái đã chuyển sang "Đã hủy".');

                // If currently viewing pending approval filter, refresh to remove rejected invoice
                if (currentFilter === 'pending-approval') {
                    filterInvoices('pending-approval');
                }

                // Refresh the display
                updateStatistics();
                renderInvoices();

                console.log('✅ Invoice rejected successfully');
            } catch (error) {
                console.error('❌ Error rejecting invoice:', error);
                alert('Có lỗi xảy ra khi từ chối hóa đơn. Vui lòng thử lại.');
            }
        }

        // Confirm cash payment
        function confirmCashPayment(invoiceId) {
            // Check permission
            if (!adminAuth || !adminAuth.hasPermission('approve_invoices')) {
                alert('Bạn không có quyền xác nhận thanh toán!');
                return;
            }

            if (!confirm('Bạn có chắc chắn đã nhận được thanh toán tiền mặt cho hóa đơn này?')) {
                return;
            }

            try {
                console.log('🔍 Confirming cash payment:', invoiceId);

                // Update invoice status to confirmed
                updateInvoiceStatus(invoiceId, 'confirmed');

                // Show success message
                alert('✅ Đã xác nhận thanh toán tiền mặt!\nTrạng thái đã chuyển sang "Đã xác nhận".');

                // If currently viewing pending filter, refresh to remove confirmed invoice
                if (currentFilter === 'pending') {
                    filterInvoices('pending');
                }

                // Refresh the display
                updateStatistics();
                renderInvoices();

                console.log('✅ Cash payment confirmed successfully');
            } catch (error) {
                console.error('❌ Error confirming cash payment:', error);
                alert('Có lỗi xảy ra khi xác nhận thanh toán. Vui lòng thử lại.');
            }
        }

        // Update invoice status in local data
        function updateInvoiceStatus(invoiceId, newStatus) {
            const invoice = allInvoices.find(inv => inv.id == invoiceId);
            if (invoice) {
                console.log(`🔄 Updating invoice ${invoiceId} from ${invoice.status} to ${newStatus}`);
                invoice.status = newStatus;
                invoice.updatedAt = new Date().toISOString();

                // Save to localStorage immediately
                saveInvoiceHistory();

                // Also update in invoiceList if exists (for payment flow compatibility)
                const invoiceList = localStorage.getItem('invoiceList');
                if (invoiceList) {
                    try {
                        const invoices = JSON.parse(invoiceList);
                        const paymentInvoice = invoices.find(inv =>
                            (inv.invoice && inv.invoice.id === invoiceId) || inv.id === invoiceId
                        );
                        if (paymentInvoice) {
                            if (paymentInvoice.invoice) {
                                paymentInvoice.invoice.status = newStatus;
                            } else {
                                paymentInvoice.status = newStatus;
                            }
                            localStorage.setItem('invoiceList', JSON.stringify(invoices));
                        }
                    } catch (error) {
                        console.warn('Error updating invoiceList:', error);
                    }
                }

                console.log(`✅ Invoice ${invoiceId} status updated successfully`);
            } else {
                console.error(`❌ Invoice ${invoiceId} not found for status update`);
            }
        }

        // View transfer proof image
        function viewTransferProof(invoiceId) {
            const invoice = allInvoices.find(inv => inv.id === invoiceId);
            if (!invoice || !invoice.transfer_proof) {
                alert('Không tìm thấy hình ảnh chuyển khoản cho hóa đơn này.');
                return;
            }

            const modal = document.getElementById('transferProofModal');
            const content = document.getElementById('transferProofContent');

            content.innerHTML = `
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">
                        <strong>Hóa đơn:</strong> ${invoice.invoiceNumber || 'N/A'} |
                        <strong>Khách hàng:</strong> ${invoice.customerName || 'N/A'}
                    </p>
                    <p class="text-sm text-gray-600 mb-4">
                        <strong>Số tiền:</strong> ${formatPrice(invoice.total || 0)}
                    </p>
                </div>
                <div class="border border-gray-300 rounded-lg overflow-hidden">
                    <img src="${invoice.transfer_proof}"
                         alt="Hình ảnh chuyển khoản"
                         class="max-w-full max-h-96 object-contain mx-auto"
                         onerror="this.parentElement.innerHTML='<div class=\\'p-8 text-gray-500\\'>❌ Không thể tải hình ảnh</div>'">
                </div>
                <p class="text-xs text-gray-500 mt-2">
                    Click vào hình ảnh để xem kích thước đầy đủ
                </p>
            `;

            modal.classList.remove('hidden');
        }

        // Close transfer proof modal
        function closeTransferProofModal() {
            const modal = document.getElementById('transferProofModal');
            modal.classList.add('hidden');
        }

        // Delete invoice
        function deleteInvoice(invoiceId) {
            // Check permission
            if (!adminAuth || !adminAuth.hasPermission('delete_invoices')) {
                alert('Bạn không có quyền xóa hóa đơn!');
                return;
            }

            const invoice = allInvoices.find(inv => inv.id === invoiceId);
            if (!invoice) {
                alert('Không tìm thấy hóa đơn!');
                return;
            }

            const confirmMessage = `Bạn có chắc chắn muốn xóa hóa đơn này?\n\n` +
                                 `📋 Mã hóa đơn: ${invoice.invoiceNumber || 'N/A'}\n` +
                                 `👤 Khách hàng: ${invoice.customerName || 'N/A'}\n` +
                                 `💰 Số tiền: ${formatPrice(invoice.total || 0)}\n\n` +
                                 `⚠️ Hành động này không thể hoàn tác!`;

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                // Remove from allInvoices array
                const index = allInvoices.findIndex(inv => inv.id === invoiceId);
                if (index !== -1) {
                    allInvoices.splice(index, 1);
                }

                // Update localStorage
                saveInvoiceHistory();

                // Also remove from invoiceList if exists
                let invoiceList = JSON.parse(localStorage.getItem('invoiceList')) || [];
                invoiceList = invoiceList.filter(inv => {
                    const invId = inv.invoice?.id_hoa_don || inv.id;
                    return invId !== invoiceId;
                });
                localStorage.setItem('invoiceList', JSON.stringify(invoiceList));

                console.log('✅ Invoice deleted successfully:', invoiceId);
                alert('✅ Hóa đơn đã được xóa thành công!');

                // Refresh display
                updateStatistics();
                renderInvoices();

            } catch (error) {
                console.error('❌ Error deleting invoice:', error);
                alert('Có lỗi xảy ra khi xóa hóa đơn. Vui lòng thử lại!');
            }
        }

        // Edit invoice
        function editInvoice(invoiceId) {
            // Check permission
            if (!adminAuth || !adminAuth.hasPermission('edit_invoices')) {
                alert('Bạn không có quyền sửa hóa đơn!');
                return;
            }

            const invoice = allInvoices.find(inv => inv.id === invoiceId);
            if (!invoice) {
                alert('Không tìm thấy hóa đơn!');
                return;
            }

            // Populate edit form
            document.getElementById('editInvoiceNumber').value = invoice.invoiceNumber || '';
            document.getElementById('editStatus').value = invoice.status || 'pending';
            document.getElementById('editCustomerName').value = invoice.customerName || '';
            document.getElementById('editCustomerEmail').value = invoice.customerEmail || '';
            document.getElementById('editCustomerPhone').value = invoice.customerPhone || '';
            document.getElementById('editOrderType').value = invoice.orderType || 'dine-in';
            document.getElementById('editPaymentMethod').value = invoice.payment_method || 'cash';
            document.getElementById('editDeliveryAddress').value = invoice.address || '';
            document.getElementById('editNotes').value = invoice.notes || '';

            // Show/hide delivery address section
            const deliverySection = document.getElementById('editDeliveryAddressSection');
            if (invoice.orderType === 'delivery') {
                deliverySection.classList.remove('hidden');
            } else {
                deliverySection.classList.add('hidden');
            }

            // Populate items list
            populateEditItemsList(invoice.items || []);

            // Store current invoice ID for saving
            document.getElementById('editInvoiceForm').dataset.invoiceId = invoiceId;

            // Show modal
            document.getElementById('editInvoiceModal').classList.remove('hidden');
        }

        // Populate edit items list
        function populateEditItemsList(items) {
            const container = document.getElementById('editItemsList');
            container.innerHTML = '';

            let total = 0;

            items.forEach((item, index) => {
                const itemTotal = (item.price || 0) * (item.quantity || 1);
                total += itemTotal;

                const itemDiv = document.createElement('div');
                itemDiv.className = 'flex items-center justify-between p-3 border border-gray-200 rounded-lg';
                itemDiv.innerHTML = `
                    <div class="flex-1">
                        <input type="text" value="${item.name || item.ten_mon || ''}"
                               class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                               onchange="updateEditItem(${index}, 'name', this.value)">
                    </div>
                    <div class="w-24 mx-2">
                        <input type="number" value="${item.quantity || item.so_luong || 1}" min="1"
                               class="w-full px-2 py-1 border border-gray-300 rounded text-sm text-center"
                               onchange="updateEditItem(${index}, 'quantity', this.value)">
                    </div>
                    <div class="w-32 mx-2">
                        <input type="number" value="${item.price || item.don_gia || 0}" min="0"
                               class="w-full px-2 py-1 border border-gray-300 rounded text-sm text-right"
                               onchange="updateEditItem(${index}, 'price', this.value)">
                    </div>
                    <div class="w-32 text-right font-medium">
                        ${itemTotal.toLocaleString('vi-VN')}đ
                    </div>
                    <button type="button" onclick="removeEditItem(${index})"
                            class="ml-2 text-red-600 hover:text-red-800">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(itemDiv);
            });

            // Update total
            document.getElementById('editTotalAmount').textContent = `${total.toLocaleString('vi-VN')}đ`;
        }

        // Update edit item
        function updateEditItem(index, field, value) {
            const invoiceId = document.getElementById('editInvoiceForm').dataset.invoiceId;
            const invoice = allInvoices.find(inv => inv.id === invoiceId);

            if (!invoice || !invoice.items || !invoice.items[index]) return;

            if (field === 'name') {
                invoice.items[index].name = value;
                invoice.items[index].ten_mon = value;
            } else if (field === 'quantity') {
                const qty = parseInt(value) || 1;
                invoice.items[index].quantity = qty;
                invoice.items[index].so_luong = qty;
            } else if (field === 'price') {
                const price = parseFloat(value) || 0;
                invoice.items[index].price = price;
                invoice.items[index].don_gia = price;
            }

            // Recalculate total
            populateEditItemsList(invoice.items);
        }

        // Remove edit item
        function removeEditItem(index) {
            const invoiceId = document.getElementById('editInvoiceForm').dataset.invoiceId;
            const invoice = allInvoices.find(inv => inv.id === invoiceId);

            if (!invoice || !invoice.items) return;

            if (invoice.items.length <= 1) {
                alert('Hóa đơn phải có ít nhất 1 món ăn!');
                return;
            }

            if (confirm('Bạn có chắc chắn muốn xóa món ăn này?')) {
                invoice.items.splice(index, 1);
                populateEditItemsList(invoice.items);
            }
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editInvoiceModal').classList.add('hidden');
        }

        // Save invoice changes
        function saveInvoiceChanges(invoiceId, formData) {
            const invoice = allInvoices.find(inv => inv.id === invoiceId);
            if (!invoice) {
                throw new Error('Invoice not found');
            }

            // Update invoice data
            invoice.status = formData.status;
            invoice.customerName = formData.customerName;
            invoice.customerEmail = formData.customerEmail;
            invoice.customerPhone = formData.customerPhone;
            invoice.orderType = formData.orderType;
            invoice.payment_method = formData.paymentMethod;
            invoice.address = formData.deliveryAddress;
            invoice.notes = formData.notes;

            // Calculate new total
            invoice.total = invoice.items.reduce((sum, item) => {
                return sum + ((item.price || 0) * (item.quantity || 1));
            }, 0);

            // Update localStorage
            saveInvoiceHistory();

            // Also update invoiceList if exists
            let invoiceList = JSON.parse(localStorage.getItem('invoiceList')) || [];
            const listIndex = invoiceList.findIndex(inv => {
                const invId = inv.invoice?.id_hoa_don || inv.id;
                return invId === invoiceId;
            });

            if (listIndex !== -1) {
                // Update the invoice in the list
                if (invoiceList[listIndex].invoice) {
                    invoiceList[listIndex].invoice.trang_thai = mapStatusToBackend(formData.status);
                    invoiceList[listIndex].invoice.tong_tien = invoice.total;
                    invoiceList[listIndex].invoice.dia_chi_giao_hang = formData.deliveryAddress;
                    invoiceList[listIndex].invoice.ghi_chu = formData.notes;
                    invoiceList[listIndex].invoice.payment_method = formData.paymentMethod;

                    // Update customer info
                    if (invoiceList[listIndex].invoice.customer_info) {
                        invoiceList[listIndex].invoice.customer_info.full_name = formData.customerName;
                        invoiceList[listIndex].invoice.customer_info.email = formData.customerEmail;
                        invoiceList[listIndex].invoice.customer_info.phone = formData.customerPhone;
                    }
                }
                localStorage.setItem('invoiceList', JSON.stringify(invoiceList));
            }

            console.log('✅ Invoice updated successfully:', invoiceId);
        }

        // Map frontend status to backend status
        function mapStatusToBackend(frontendStatus) {
            switch (frontendStatus) {
                case 'pending': return 'cho_xac_nhan';
                case 'pending-approval': return 'cho_duyet';
                case 'confirmed': return 'da_xac_nhan';
                case 'completed': return 'hoan_thanh';
                case 'cancelled': return 'da_huy';
                default: return 'cho_xac_nhan';
            }
        }

        // Show/hide states
        function showInvoiceList() {
            document.getElementById('invoiceList').classList.remove('hidden');
            document.getElementById('emptyState').classList.add('hidden');
            document.getElementById('noResultsState').classList.add('hidden');
        }

        function showEmptyState() {
            document.getElementById('invoiceList').classList.add('hidden');
            document.getElementById('emptyState').classList.remove('hidden');
            document.getElementById('noResultsState').classList.add('hidden');
            document.getElementById('pagination').classList.add('hidden');
        }

        function showNoResults() {
            document.getElementById('invoiceList').classList.add('hidden');
            document.getElementById('emptyState').classList.add('hidden');
            document.getElementById('noResultsState').classList.remove('hidden');
            document.getElementById('pagination').classList.add('hidden');
        }

        // Pagination
        function renderPagination(totalPages) {
            const paginationContainer = document.getElementById('pagination');
            const pageNumbersContainer = document.getElementById('pageNumbers');

            if (totalPages <= 1) {
                paginationContainer.classList.add('hidden');
                return;
            }

            paginationContainer.classList.remove('hidden');
            pageNumbersContainer.innerHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                const pageButton = document.createElement('button');
                pageButton.className = `px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 ${i === currentPage ? 'bg-blue-600 text-white border-blue-600' : ''}`;
                pageButton.textContent = i;
                pageButton.onclick = () => changePage(i);
                pageNumbersContainer.appendChild(pageButton);
            }
        }

        function changePage(page) {
            if (page === 'prev') {
                if (currentPage > 1) currentPage--;
            } else if (page === 'next') {
                const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
                if (currentPage < totalPages) currentPage++;
            } else {
                currentPage = page;
            }
            renderInvoices();
        }

        // Clear all test data (for debugging)
        function clearAllTestData() {
            if (!adminAuth || !adminAuth.isAdmin()) {
                alert('Chỉ admin mới có thể xóa dữ liệu test!');
                return;
            }

            if (confirm('⚠️ Bạn có chắc chắn muốn xóa TẤT CẢ dữ liệu test?\nHành động này không thể hoàn tác!')) {
                localStorage.removeItem('invoiceHistory');
                localStorage.removeItem('invoiceList');
                allInvoices = [];
                filteredInvoices = [];
                updateStatistics();
                renderInvoices();
                alert('✅ Đã xóa tất cả dữ liệu test!');
            }
        }

        // Add sample pending invoice for testing
        function addSamplePendingInvoice() {
            // Check permission
            if (!adminAuth || !adminAuth.isAdmin()) {
                alert('Chỉ admin mới có thể tạo hóa đơn test!');
                return;
            }

            const randomId = 'pending_' + Date.now();
            const randomInvoiceNumber = 'HD' + String(Math.floor(Math.random() * 999999)).padStart(6, '0');

            const sampleInvoice = {
                id: randomId,
                invoiceNumber: randomInvoiceNumber,
                customerName: 'Khách hàng Test',
                customerEmail: '<EMAIL>',
                customerPhone: '**********',
                items: [
                    { name: 'Phở Bò Tái', quantity: 1, price: 65000, total: 65000 },
                    { name: 'Trà Đá', quantity: 2, price: 5000, total: 10000 }
                ],
                total: 75000,
                orderType: 'dine-in',
                status: 'pending-approval',
                payment_method: 'bank_transfer',
                payment_status: 'pending',
                createdAt: new Date().toISOString(),
                address: null,
                notes: 'Hóa đơn test - Thanh toán chuyển khoản ngân hàng',
                transfer_proof: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmZGY0IiBzdHJva2U9IiNhN2YzZDAiIHN0cm9rZS13aWR0aD0iMiIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iMjAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiMwNjU5NGYiPkjDrG5oIMOhbmggQ2h1eeG7g24gS2hvYW4gVGVzdDwvdGV4dD4KICA8dGV4dCB4PSI1MCUiIHk9IjQwJSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMDY1OTRmIj5T4buRIHRp4buBbjogMTUwLDAwMCBWTsSTPC90ZXh0PgogIDx0ZXh0IHg9IjUwJSIgeT0iNjAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiMwNzc0OWIiPk5nw6J5OiAke25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCd2aS1WTicpfTwvdGV4dD4KICA8dGV4dCB4PSI1MCUiIHk9Ijc1JSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSIjNmI3Mjc5Ij5Iw6xuaCDhuqNuaCBt4bqrdSBjaG8gdGVzdDwvdGV4dD4KICA8Y2lyY2xlIGN4PSIzNTAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9IiMxMGI5ODEiLz4KICA8dGV4dCB4PSIzNTAiIHk9IjU1IiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IndoaXRlIj7inJM8L3RleHQ+Cjwvc3ZnPg=='
            };

            allInvoices.unshift(sampleInvoice);
            saveInvoiceHistory();

            alert('✅ Đã tạo hóa đơn mẫu chờ duyệt!\nMã hóa đơn: ' + randomInvoiceNumber);

            // Refresh display
            updateStatistics();
            filterInvoices('pending-approval'); // Auto switch to pending approval filter
        }

        // Add sample cash payment invoice for testing
        function addSampleCashInvoice() {
            // Check permission
            if (!adminAuth || !adminAuth.isAdmin()) {
                alert('Chỉ admin mới có thể tạo hóa đơn test!');
                return;
            }

            const randomId = 'cash_' + Date.now();
            const randomInvoiceNumber = 'HD' + String(Math.floor(Math.random() * 999999)).padStart(6, '0');

            const sampleInvoice = {
                id: randomId,
                invoiceNumber: randomInvoiceNumber,
                customerName: 'Khách hàng Tiền Mặt',
                customerEmail: '<EMAIL>',
                customerPhone: '0123456789',
                items: [
                    { name: 'Bún Bò Huế', quantity: 1, price: 55000, total: 55000 },
                    { name: 'Nước Cam', quantity: 1, price: 25000, total: 25000 }
                ],
                total: 80000,
                orderType: 'dine-in',
                status: 'pending',
                payment_method: 'cash',
                payment_status: 'pending',
                createdAt: new Date().toISOString(),
                address: null,
                notes: 'Hóa đơn test - Thanh toán tiền mặt tại quầy'
            };

            // Add to invoices list
            allInvoices.unshift(sampleInvoice);
            saveInvoiceHistory();

            alert('✅ Đã tạo hóa đơn mẫu tiền mặt!\nMã hóa đơn: ' + randomInvoiceNumber);

            // Refresh display
            updateStatistics();
            filterInvoices('pending'); // Auto switch to pending filter
        }

        // Refresh list
        function refreshList() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('invoiceList').classList.add('hidden');

            setTimeout(() => {
                loadInvoices();
            }, 500);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applySearch(this.value.trim());
                }, 300);
            });

            // Sort functionality
            document.getElementById('sortSelect').addEventListener('change', function() {
                sortInvoices(this.value);
            });

            // Close modals when clicking outside
            document.getElementById('transferProofModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeTransferProofModal();
                }
            });

            document.getElementById('editInvoiceModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditModal();
                }
            });

            // Edit form submission
            document.getElementById('editInvoiceForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const invoiceId = this.dataset.invoiceId;
                if (!invoiceId) {
                    alert('Không tìm thấy ID hóa đơn!');
                    return;
                }

                try {
                    const formData = {
                        status: document.getElementById('editStatus').value,
                        customerName: document.getElementById('editCustomerName').value.trim(),
                        customerEmail: document.getElementById('editCustomerEmail').value.trim(),
                        customerPhone: document.getElementById('editCustomerPhone').value.trim(),
                        orderType: document.getElementById('editOrderType').value,
                        paymentMethod: document.getElementById('editPaymentMethod').value,
                        deliveryAddress: document.getElementById('editDeliveryAddress').value.trim(),
                        notes: document.getElementById('editNotes').value.trim()
                    };

                    // Validate required fields
                    if (!formData.customerName) {
                        alert('Vui lòng nhập tên khách hàng!');
                        return;
                    }

                    if (!formData.customerEmail) {
                        alert('Vui lòng nhập email khách hàng!');
                        return;
                    }

                    if (!formData.customerPhone) {
                        alert('Vui lòng nhập số điện thoại khách hàng!');
                        return;
                    }

                    // Save changes
                    saveInvoiceChanges(invoiceId, formData);

                    alert('✅ Cập nhật hóa đơn thành công!');
                    closeEditModal();

                    // Refresh display
                    updateStatistics();
                    renderInvoices();

                } catch (error) {
                    console.error('❌ Error saving invoice:', error);
                    alert('Có lỗi xảy ra khi lưu thay đổi. Vui lòng thử lại!');
                }
            });

            // Order type change in edit form
            document.getElementById('editOrderType').addEventListener('change', function() {
                const deliverySection = document.getElementById('editDeliveryAddressSection');
                if (this.value === 'delivery') {
                    deliverySection.classList.remove('hidden');
                } else {
                    deliverySection.classList.add('hidden');
                }
            });

            // Load invoices
            loadInvoices();
        });

        // Revenue Statistics Functions
        function showRevenueStats() {
            // Check permission
            if (!adminAuth || !adminAuth.isAdmin()) {
                alert('Chỉ admin mới có thể xem thống kê doanh thu!');
                return;
            }

            // Set default date range (last 30 days)
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];

            // Show modal
            document.getElementById('revenueStatsModal').classList.remove('hidden');

            // Load initial stats
            updateRevenueStats();
        }

        function closeRevenueStats() {
            document.getElementById('revenueStatsModal').classList.add('hidden');
        }

        function updateRevenueStats() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Vui lòng chọn khoảng thời gian!');
                return;
            }

            const start = new Date(startDate);
            const end = new Date(endDate + 'T23:59:59'); // Include end of day

            // Filter invoices by date range and confirmed status
            const filteredInvoices = allInvoices.filter(invoice => {
                const invoiceDate = new Date(invoice.createdAt || invoice.timestamp);
                return invoiceDate >= start &&
                       invoiceDate <= end &&
                       invoice.status === 'confirmed';
            });

            // Calculate statistics
            const stats = calculateRevenueStats(filteredInvoices, start, end);

            // Update UI
            updateStatsDisplay(stats);
            updateDailyChart(stats.dailyRevenue);
            updatePaymentMethodStats(stats.paymentMethods);
            updateTopDishesStats(stats.topDishes);
        }

        function calculateRevenueStats(invoices, startDate, endDate) {
            const totalOrders = invoices.length;
            const totalRevenue = invoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
            const averageOrder = totalOrders > 0 ? totalRevenue / totalOrders : 0;

            // Today's orders
            const today = new Date().toDateString();
            const todayOrders = invoices.filter(inv => {
                const invDate = new Date(inv.createdAt || inv.timestamp).toDateString();
                return invDate === today;
            }).length;

            // Daily revenue breakdown
            const dailyRevenue = {};
            const currentDate = new Date(startDate);

            // Initialize all dates with 0
            while (currentDate <= endDate) {
                const dateStr = currentDate.toISOString().split('T')[0];
                dailyRevenue[dateStr] = 0;
                currentDate.setDate(currentDate.getDate() + 1);
            }

            // Calculate actual daily revenue
            invoices.forEach(invoice => {
                const dateStr = new Date(invoice.createdAt || invoice.timestamp).toISOString().split('T')[0];
                if (dailyRevenue.hasOwnProperty(dateStr)) {
                    dailyRevenue[dateStr] += invoice.total || 0;
                }
            });

            // Payment method breakdown
            const paymentMethods = {};
            invoices.forEach(invoice => {
                const method = invoice.payment_method || 'cash';
                if (!paymentMethods[method]) {
                    paymentMethods[method] = { count: 0, revenue: 0 };
                }
                paymentMethods[method].count++;
                paymentMethods[method].revenue += invoice.total || 0;
            });

            // Top dishes
            const dishStats = {};
            invoices.forEach(invoice => {
                (invoice.items || []).forEach(item => {
                    const dishName = item.ten_mon || item.name || 'Món ăn';
                    if (!dishStats[dishName]) {
                        dishStats[dishName] = { quantity: 0, revenue: 0 };
                    }
                    dishStats[dishName].quantity += item.so_luong || item.quantity || 1;
                    dishStats[dishName].revenue += item.thanh_tien || (item.don_gia * item.so_luong) || 0;
                });
            });

            const topDishes = Object.entries(dishStats)
                .sort((a, b) => b[1].revenue - a[1].revenue)
                .slice(0, 5);

            return {
                totalOrders,
                totalRevenue,
                averageOrder,
                todayOrders,
                dailyRevenue,
                paymentMethods,
                topDishes
            };
        }

        function updateStatsDisplay(stats) {
            document.getElementById('totalOrders').textContent = stats.totalOrders;
            document.getElementById('totalRevenue').textContent = formatPrice(stats.totalRevenue);
            document.getElementById('averageOrder').textContent = formatPrice(stats.averageOrder);
            document.getElementById('todayOrders').textContent = stats.todayOrders;
        }

        function updateDailyChart(dailyRevenue) {
            const chartContainer = document.getElementById('dailyRevenueChart');
            const dates = Object.keys(dailyRevenue).sort();
            const revenues = dates.map(date => dailyRevenue[date]);

            if (dates.length === 0) {
                chartContainer.innerHTML = `
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-bar text-4xl mb-2"></i>
                        <p>Không có dữ liệu trong khoảng thời gian này</p>
                    </div>
                `;
                return;
            }

            const maxRevenue = Math.max(...revenues);
            const chartHTML = dates.map((date, index) => {
                const revenue = revenues[index];
                const height = maxRevenue > 0 ? (revenue / maxRevenue) * 200 : 0;
                const dateObj = new Date(date);
                const dayMonth = `${dateObj.getDate()}/${dateObj.getMonth() + 1}`;

                return `
                    <div class="flex flex-col items-center mx-1">
                        <div class="bg-blue-500 rounded-t" style="height: ${height}px; width: 30px; min-height: 2px;"></div>
                        <div class="text-xs text-gray-600 mt-2 transform -rotate-45 origin-top-left">${dayMonth}</div>
                        <div class="text-xs text-gray-800 font-medium mt-1">${formatPrice(revenue)}</div>
                    </div>
                `;
            }).join('');

            chartContainer.innerHTML = `
                <div class="flex items-end justify-center space-x-1 h-64 overflow-x-auto">
                    ${chartHTML}
                </div>
            `;
        }

        function updatePaymentMethodStats(paymentMethods) {
            const container = document.getElementById('paymentMethodStats');
            const total = Object.values(paymentMethods).reduce((sum, method) => sum + method.revenue, 0);

            if (Object.keys(paymentMethods).length === 0) {
                container.innerHTML = '<p class="text-gray-500">Không có dữ liệu</p>';
                return;
            }

            const methodsHTML = Object.entries(paymentMethods).map(([method, data]) => {
                const percentage = total > 0 ? (data.revenue / total * 100).toFixed(1) : 0;
                const methodText = getPaymentMethodText(method);

                return `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                            <span class="font-medium">${methodText}</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold">${formatPrice(data.revenue)}</div>
                            <div class="text-sm text-gray-600">${data.count} đơn (${percentage}%)</div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = methodsHTML;
        }

        function updateTopDishesStats(topDishes) {
            const container = document.getElementById('topDishesStats');

            if (topDishes.length === 0) {
                container.innerHTML = '<p class="text-gray-500">Không có dữ liệu</p>';
                return;
            }

            const dishesHTML = topDishes.map(([dishName, data], index) => {
                const rank = index + 1;
                const rankColors = ['text-yellow-600', 'text-gray-600', 'text-orange-600', 'text-blue-600', 'text-green-600'];
                const rankColor = rankColors[index] || 'text-gray-600';

                return `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                <span class="font-bold ${rankColor}">${rank}</span>
                            </div>
                            <span class="font-medium">${dishName}</span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold">${formatPrice(data.revenue)}</div>
                            <div class="text-sm text-gray-600">${data.quantity} phần</div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = dishesHTML;
        }
    </script>
    <!-- Custom Styles for Enhanced UI -->
    <style>
        /* Modern gradient background */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* Enhanced header styling */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced cards */
        .invoice-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .invoice-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        /* Enhanced statistics cards */
        .bg-white {
            background: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .bg-white:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced status badges */
        .status-pending {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
            border-radius: 20px;
            font-weight: 600;
        }

        .status-pending-approval {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            border-radius: 20px;
            font-weight: 600;
        }

        .status-confirmed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            border-radius: 20px;
            font-weight: 600;
        }

        .status-cancelled {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
            border-radius: 20px;
            font-weight: 600;
        }

        /* Enhanced scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        /* Container styling */
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Enhanced modal */
        #revenueStatsModal .bg-white {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
    </style>





</body>
</html>
