<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Ràng Buộc Chặt Chẽ - Auto Logout</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root { --primary-color: #e53e3e; }
        .bg-primary { background-color: var(--primary-color); }
        .text-primary { color: var(--primary-color); }
        .activity-detected { background-color: #10B981; color: white; }
        .activity-ignored { background-color: #6B7280; color: white; }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">Demo Ràng Buộc Chặt Chẽ - Activity Detection</h1>
        
        <!-- Explanation -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">Cách Hoạt Động</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="p-4 bg-green-50 border border-green-200 rounded">
                    <h3 class="font-bold text-green-800 mb-2">✅ Hoạt Động Được Tính (Reset Timer)</h3>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li><i class="fas fa-mouse-pointer mr-2"></i>Click chuột</li>
                        <li><i class="fas fa-hand-pointer mr-2"></i>Nhấn chuột (mousedown)</li>
                        <li><i class="fas fa-keyboard mr-2"></i>Gõ phím (keypress/keydown)</li>
                        <li><i class="fas fa-hand-paper mr-2"></i>Chạm màn hình (touchstart)</li>
                    </ul>
                </div>
                <div class="p-4 bg-red-50 border border-red-200 rounded">
                    <h3 class="font-bold text-red-800 mb-2">❌ Hoạt Động KHÔNG Được Tính</h3>
                    <ul class="text-sm text-red-700 space-y-1">
                        <li><i class="fas fa-arrows-alt mr-2"></i>Di chuyển chuột (mousemove)</li>
                        <li><i class="fas fa-scroll mr-2"></i>Cuộn trang (scroll)</li>
                        <li><i class="fas fa-eye mr-2"></i>Focus/Blur window</li>
                        <li><i class="fas fa-clock mr-2"></i>Chỉ mở trang mà không tương tác</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Activity Monitor -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">Monitor Hoạt Động Real-time</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="text-center p-4 bg-blue-50 rounded">
                    <h3 class="font-bold text-blue-600">Thời gian không tương tác</h3>
                    <div id="inactivityTimer" class="text-3xl font-mono text-blue-800">00:00</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded">
                    <h3 class="font-bold text-red-600">Thời gian đến auto logout</h3>
                    <div id="logoutTimer" class="text-3xl font-mono text-red-800">02:00</div>
                </div>
            </div>
            <div id="activityLog" class="h-32 overflow-y-auto border rounded p-2 bg-gray-50 text-sm">
                <p class="text-gray-500">Bắt đầu monitor hoạt động...</p>
            </div>
        </div>

        <!-- Test Areas -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Real Interactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 text-green-600">Tương Tác Thực Sự (Reset Timer)</h3>
                <div class="space-y-3">
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded">
                        <i class="fas fa-mouse-pointer mr-2"></i>Click Button
                    </button>
                    <input type="text" placeholder="Gõ vào đây để test keypress..." 
                           class="w-full p-2 border rounded focus:border-green-500">
                    <textarea placeholder="Gõ vào textarea..." 
                              class="w-full p-2 border rounded h-20 focus:border-green-500"></textarea>
                </div>
            </div>

            <!-- Non-Interactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 text-red-600">Không Phải Tương Tác (Không Reset)</h3>
                <div class="space-y-3">
                    <div class="p-4 border-2 border-dashed border-red-300 rounded text-center">
                        <p class="text-red-600 mb-2">Di chuyển chuột trong vùng này</p>
                        <p class="text-sm text-red-500">(Sẽ KHÔNG reset timer)</p>
                    </div>
                    <div class="h-32 overflow-y-scroll border rounded p-2 bg-gray-50">
                        <p>Cuộn nội dung này sẽ KHÔNG reset timer</p>
                        <p>Dòng 1</p><p>Dòng 2</p><p>Dòng 3</p><p>Dòng 4</p><p>Dòng 5</p>
                        <p>Dòng 6</p><p>Dòng 7</p><p>Dòng 8</p><p>Dòng 9</p><p>Dòng 10</p>
                        <p>Dòng 11</p><p>Dòng 12</p><p>Dòng 13</p><p>Dòng 14</p><p>Dòng 15</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-bold text-yellow-800 mb-3">
                <i class="fas fa-lightbulb mr-2"></i>Hướng Dẫn Test
            </h3>
            <ol class="list-decimal list-inside space-y-2 text-yellow-700">
                <li>Mở trang này và quan sát timer bắt đầu đếm</li>
                <li>Thử di chuyển chuột → Timer vẫn tiếp tục đếm</li>
                <li>Thử cuộn trang → Timer vẫn tiếp tục đếm</li>
                <li>Click vào button xanh → Timer reset về 00:00</li>
                <li>Gõ vào input/textarea → Timer reset về 00:00</li>
                <li>Để 2 phút không click/gõ phím → Tự động logout</li>
            </ol>
        </div>
    </div>

    <script>
        let startTime = Date.now();
        let lastActivity = Date.now();
        let timerInterval;

        // Activity tracking - chỉ tương tác thực sự
        const realInteractionEvents = ['mousedown', 'keydown', 'keypress', 'touchstart', 'click'];
        const ignoredEvents = ['mousemove', 'scroll', 'focus', 'blur'];

        function logActivity(eventType, isRealInteraction) {
            const log = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = isRealInteraction ? 'activity-detected' : 'activity-ignored';
            const icon = isRealInteraction ? '✅' : '❌';
            
            const logEntry = document.createElement('div');
            logEntry.className = `p-1 rounded mb-1 ${className}`;
            logEntry.innerHTML = `${icon} [${timestamp}] ${eventType} ${isRealInteraction ? '(Timer Reset)' : '(Ignored)'}`;
            
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;

            if (isRealInteraction) {
                lastActivity = Date.now();
            }
        }

        function updateTimers() {
            const now = Date.now();
            const inactiveTime = now - lastActivity;
            const timeToLogout = Math.max(0, (2 * 60 * 1000) - inactiveTime);

            // Update displays
            const inactiveMinutes = Math.floor(inactiveTime / 60000);
            const inactiveSeconds = Math.floor((inactiveTime % 60000) / 1000);
            document.getElementById('inactivityTimer').textContent = 
                `${inactiveMinutes.toString().padStart(2, '0')}:${inactiveSeconds.toString().padStart(2, '0')}`;

            const logoutMinutes = Math.floor(timeToLogout / 60000);
            const logoutSeconds = Math.floor((timeToLogout % 60000) / 1000);
            document.getElementById('logoutTimer').textContent = 
                `${logoutMinutes.toString().padStart(2, '0')}:${logoutSeconds.toString().padStart(2, '0')}`;

            // Auto logout simulation
            if (timeToLogout <= 0) {
                alert('🚪 AUTO LOGOUT! Đã 2 phút không có tương tác thực sự.');
                lastActivity = Date.now(); // Reset for demo
            }
        }

        // Setup event listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Real interaction events
            realInteractionEvents.forEach(event => {
                document.addEventListener(event, (e) => {
                    logActivity(event, true);
                }, true);
            });

            // Ignored events (for demonstration)
            ignoredEvents.forEach(event => {
                document.addEventListener(event, (e) => {
                    logActivity(event, false);
                }, true);
            });

            // Start timer
            timerInterval = setInterval(updateTimers, 1000);
            
            // Initial log
            logActivity('Page Loaded', false);
        });
    </script>
</body>
</html>
